package com.rabbitmq.learning.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 网关服务启动类
 * 
 * 该服务主要功能：
 * 1. 统一入口 - 作为所有微服务的统一入口
 * 2. 路由转发 - 根据请求路径转发到对应的微服务
 * 3. 负载均衡 - 在多个服务实例之间进行负载均衡
 * 4. 限流控制 - 防止服务被过量请求压垮
 * 5. 跨域处理 - 处理前端跨域请求
 * 6. 请求日志 - 记录所有通过网关的请求
 * 7. 异常处理 - 统一处理服务异常和错误响应
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
public class GatewayServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayServiceApplication.class, args);
        System.out.println("========================================");
        System.out.println("网关服务启动成功！");
        System.out.println("网关地址: http://localhost:8080");
        System.out.println("健康检查: http://localhost:8080/actuator/health");
        System.out.println("========================================");
        System.out.println("服务路由信息：");
        System.out.println("生产者服务: http://localhost:8080/producer/**");
        System.out.println("消费者服务: http://localhost:8080/consumer/**");
        System.out.println("订单服务: http://localhost:8080/order/**");
        System.out.println("库存服务: http://localhost:8080/inventory/**");
        System.out.println("========================================");
    }
}
