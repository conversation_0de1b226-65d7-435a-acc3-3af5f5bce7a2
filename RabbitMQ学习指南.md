# RabbitMQ 深度学习指南

## 目录
1. [RabbitMQ基础概念](#rabbitmq基础概念)
2. [消息模式详解](#消息模式详解)
3. [高级特性](#高级特性)
4. [实战案例](#实战案例)
5. [性能优化](#性能优化)
6. [监控与运维](#监控与运维)

## RabbitMQ基础概念

### 核心组件

#### 1. Producer (生产者)
- **作用**: 发送消息的应用程序
- **特点**: 只负责发送消息，不关心消息如何被处理
- **示例**: 订单系统发送订单创建消息

#### 2. Consumer (消费者)
- **作用**: 接收并处理消息的应用程序
- **特点**: 监听队列，处理接收到的消息
- **示例**: 库存系统处理库存扣减消息

#### 3. Queue (队列)
- **作用**: 存储消息的缓冲区
- **特点**: FIFO（先进先出）原则
- **属性**: 
  - 持久化：服务重启后队列是否保留
  - 排他性：是否只允许一个连接使用
  - 自动删除：没有消费者时是否自动删除

#### 4. Exchange (交换机)
- **作用**: 接收生产者发送的消息，并根据路由规则分发到队列
- **类型**:
  - **Direct**: 精确匹配路由键
  - **Topic**: 模式匹配路由键
  - **Fanout**: 广播到所有绑定的队列
  - **Headers**: 根据消息头属性路由

#### 5. Binding (绑定)
- **作用**: 连接交换机和队列的规则
- **包含**: 路由键(Routing Key)和绑定键(Binding Key)

#### 6. Virtual Host (虚拟主机)
- **作用**: 逻辑隔离，类似于数据库的schema
- **特点**: 不同vhost之间完全隔离

## 消息模式详解

### 1. 简单队列模式 (Simple Queue)

```
Producer -> Queue -> Consumer
```

**特点**:
- 一对一消息传递
- 最简单的消息模式
- 不使用交换机，直接发送到队列

**使用场景**:
- 简单的任务分发
- 点对点通信

**代码示例**:
```java
// 发送消息
@PostMapping("/simple")
public Result<String> sendSimpleMessage(@RequestParam String message) {
    messageProducerService.sendSimpleMessage(message);
    return Result.success("消息发送成功");
}

// 消费消息
@RabbitListener(queues = "queue.work")
public void handleSimpleMessage(Message message, Channel channel) {
    // 处理消息逻辑
}
```

### 2. 工作队列模式 (Work Queue)

```
Producer -> Queue -> Consumer1
                  -> Consumer2
                  -> Consumer3
```

**特点**:
- 多个消费者竞争消费
- 消息轮询分发
- 提高处理效率

**使用场景**:
- 批量任务处理
- 负载均衡

**关键配置**:
```yaml
spring:
  rabbitmq:
    listener:
      simple:
        prefetch: 1  # 每次只处理一条消息
```

### 3. 发布订阅模式 (Publish/Subscribe)

```
Producer -> Fanout Exchange -> Queue1 -> Consumer1
                            -> Queue2 -> Consumer2
                            -> Queue3 -> Consumer3
```

**特点**:
- 使用Fanout交换机
- 消息广播到所有绑定队列
- 所有消费者都收到相同消息

**使用场景**:
- 系统通知
- 日志收集
- 缓存更新

### 4. 路由模式 (Routing)

```
Producer -> Direct Exchange -> Queue1 (routing.key1) -> Consumer1
                            -> Queue2 (routing.key2) -> Consumer2
```

**特点**:
- 使用Direct交换机
- 根据路由键精确匹配
- 选择性接收消息

**使用场景**:
- 日志级别分类
- 消息分类处理

### 5. 主题模式 (Topic)

```
Producer -> Topic Exchange -> Queue1 (user.*) -> Consumer1
                           -> Queue2 (*.create) -> Consumer2
                           -> Queue3 (#) -> Consumer3
```

**特点**:
- 使用Topic交换机
- 支持通配符匹配
- `*` 匹配一个单词
- `#` 匹配零个或多个单词

**使用场景**:
- 复杂的消息路由
- 按地区、类别等维度分发

## 高级特性

### 1. 消息确认机制

#### 生产者确认 (Publisher Confirms)
```java
@Configuration
public class RabbitMQConfig {
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        
        // 设置确认回调
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("消息发送成功");
            } else {
                log.error("消息发送失败: {}", cause);
            }
        });
        
        return template;
    }
}
```

#### 消费者确认 (Consumer Acknowledgments)
```java
@RabbitListener(queues = "test.queue")
public void handleMessage(Message message, Channel channel) {
    try {
        // 处理消息
        processMessage(message);
        
        // 手动确认
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    } catch (Exception e) {
        // 拒绝消息，重新入队
        channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
    }
}
```

### 2. 消息持久化

#### 交换机持久化
```java
@Bean
public DirectExchange orderExchange() {
    return ExchangeBuilder
            .directExchange("order.exchange")
            .durable(true)  // 持久化
            .build();
}
```

#### 队列持久化
```java
@Bean
public Queue orderQueue() {
    return QueueBuilder
            .durable("order.queue")  // 持久化
            .build();
}
```

#### 消息持久化
```java
// 发送持久化消息
rabbitTemplate.convertAndSend("exchange", "routing.key", message, msg -> {
    msg.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
    return msg;
});
```

### 3. 死信队列 (Dead Letter Queue)

```java
@Bean
public Queue orderQueue() {
    return QueueBuilder
            .durable("order.queue")
            .withArgument("x-dead-letter-exchange", "dead.letter.exchange")
            .withArgument("x-dead-letter-routing-key", "dead.letter")
            .withArgument("x-message-ttl", 30000)  // 30秒TTL
            .build();
}

@Bean
public Queue deadLetterQueue() {
    return QueueBuilder
            .durable("dead.letter.queue")
            .build();
}
```

**死信产生场景**:
- 消息被拒绝且不重新入队
- 消息过期
- 队列达到最大长度

### 4. 延时消息

#### 方案一：TTL + 死信队列
```java
@Bean
public Queue delayQueue() {
    return QueueBuilder
            .durable("delay.queue")
            .withArgument("x-message-ttl", 60000)  // 60秒延时
            .withArgument("x-dead-letter-exchange", "process.exchange")
            .build();
}
```

#### 方案二：延时插件
```java
@Bean
public CustomExchange delayExchange() {
    Map<String, Object> args = new HashMap<>();
    args.put("x-delayed-type", "direct");
    return new CustomExchange("delay.exchange", "x-delayed-message", true, false, args);
}

// 发送延时消息
public void sendDelayMessage(String message, int delaySeconds) {
    rabbitTemplate.convertAndSend("delay.exchange", "delay.key", message, msg -> {
        msg.getMessageProperties().setDelay(delaySeconds * 1000);
        return msg;
    });
}
```

### 5. 优先级队列

```java
@Bean
public Queue priorityQueue() {
    return QueueBuilder
            .durable("priority.queue")
            .withArgument("x-max-priority", 10)  // 最大优先级
            .build();
}

// 发送优先级消息
public void sendPriorityMessage(String message, int priority) {
    rabbitTemplate.convertAndSend("exchange", "key", message, msg -> {
        msg.getMessageProperties().setPriority(priority);
        return msg;
    });
}
```

## 实战案例

### 订单业务流程

#### 1. 订单创建流程
```java
@Transactional
public Result<Order> createOrder(OrderCreateRequest request) {
    // 1. 创建订单
    Order order = new Order();
    // ... 设置订单信息
    orderMapper.insert(order);
    
    // 2. 发送库存锁定消息
    InventoryLockMessage lockMessage = new InventoryLockMessage();
    lockMessage.setOrderNo(order.getOrderNo());
    lockMessage.setProductId(request.getProductId());
    lockMessage.setQuantity(request.getQuantity());
    
    rabbitTemplate.convertAndSend("inventory.exchange", "inventory.lock", lockMessage);
    
    // 3. 发送订单超时检查消息（15分钟后）
    OrderTimeoutMessage timeoutMessage = new OrderTimeoutMessage();
    timeoutMessage.setOrderNo(order.getOrderNo());
    
    rabbitTemplate.convertAndSend("delay.exchange", "order.timeout", timeoutMessage, msg -> {
        msg.getMessageProperties().setDelay(15 * 60 * 1000);
        return msg;
    });
    
    return Result.success(order);
}
```

#### 2. 库存锁定处理
```java
@RabbitListener(queues = "inventory.lock.queue")
public void handleInventoryLock(InventoryLockMessage message, Channel channel) {
    try {
        // 1. 检查库存
        Inventory inventory = inventoryService.getByProductId(message.getProductId());
        if (inventory.getAvailableStock() < message.getQuantity()) {
            throw new InsufficientStockException("库存不足");
        }
        
        // 2. 锁定库存
        inventoryService.lockStock(message.getProductId(), message.getQuantity(), message.getOrderNo());
        
        // 3. 确认消息
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        
    } catch (Exception e) {
        // 4. 发送库存锁定失败消息
        OrderCancelMessage cancelMessage = new OrderCancelMessage();
        cancelMessage.setOrderNo(message.getOrderNo());
        cancelMessage.setReason("库存锁定失败：" + e.getMessage());
        
        rabbitTemplate.convertAndSend("order.exchange", "order.cancel", cancelMessage);
        
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }
}
```

#### 3. 订单超时处理
```java
@RabbitListener(queues = "order.timeout.queue")
public void handleOrderTimeout(OrderTimeoutMessage message, Channel channel) {
    try {
        // 1. 查询订单状态
        Order order = orderService.getByOrderNo(message.getOrderNo());
        
        // 2. 如果仍是待支付状态，则取消订单
        if (order != null && order.getStatus() == OrderStatus.PENDING_PAYMENT) {
            orderService.cancelOrder(message.getOrderNo(), "订单超时自动取消");
        }
        
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        
    } catch (Exception e) {
        log.error("处理订单超时失败", e);
        channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
    }
}
```

### 分布式事务处理

#### 消息驱动的最终一致性
```java
// 订单支付成功后的处理
@RabbitListener(queues = "order.payment.queue")
public void handleOrderPayment(OrderPaymentMessage message, Channel channel) {
    try {
        // 1. 更新订单状态
        orderService.updateStatus(message.getOrderNo(), OrderStatus.PAID);
        
        // 2. 扣减库存
        InventoryDeductMessage deductMessage = new InventoryDeductMessage();
        deductMessage.setOrderNo(message.getOrderNo());
        deductMessage.setProductId(message.getProductId());
        deductMessage.setQuantity(message.getQuantity());
        
        rabbitTemplate.convertAndSend("inventory.exchange", "inventory.deduct", deductMessage);
        
        // 3. 发送发货消息
        ShippingMessage shippingMessage = new ShippingMessage();
        shippingMessage.setOrderNo(message.getOrderNo());
        
        rabbitTemplate.convertAndSend("shipping.exchange", "shipping.create", shippingMessage);
        
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        
    } catch (Exception e) {
        log.error("处理订单支付失败", e);
        
        // 重试机制
        if (getRetryCount(message) < MAX_RETRY_COUNT) {
            incrementRetryCount(message);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
        } else {
            // 超过重试次数，发送到死信队列
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}
```

## 性能优化

### 1. 连接池配置
```yaml
spring:
  rabbitmq:
    cache:
      connection:
        mode: channel
        size: 10
      channel:
        size: 50
        checkout-timeout: 30000
```

### 2. 批量处理
```java
@RabbitListener(queues = "batch.queue", containerFactory = "batchListenerContainerFactory")
public void handleBatchMessages(List<Message> messages, Channel channel) {
    try {
        // 批量处理消息
        processBatch(messages);
        
        // 批量确认
        if (!messages.isEmpty()) {
            Message lastMessage = messages.get(messages.size() - 1);
            channel.basicAck(lastMessage.getMessageProperties().getDeliveryTag(), true);
        }
    } catch (Exception e) {
        // 批量拒绝
        for (Message message : messages) {
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
        }
    }
}
```

### 3. 预取数量优化
```yaml
spring:
  rabbitmq:
    listener:
      simple:
        prefetch: 10  # 根据消息处理速度调整
```

## 监控与运维

### 1. 健康检查
```java
@Component
public class RabbitMQHealthIndicator implements HealthIndicator {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Override
    public Health health() {
        try {
            // 检查连接状态
            rabbitTemplate.execute(channel -> {
                channel.queueDeclarePassive("health.check.queue");
                return null;
            });
            
            return Health.up()
                    .withDetail("rabbitmq", "连接正常")
                    .build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("rabbitmq", "连接异常: " + e.getMessage())
                    .build();
        }
    }
}
```

### 2. 消息监控
```java
@Component
public class MessageMonitor {
    
    private final MeterRegistry meterRegistry;
    private final Counter messagesSent;
    private final Counter messagesReceived;
    private final Timer messageProcessingTime;
    
    public MessageMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.messagesSent = Counter.builder("rabbitmq.messages.sent")
                .description("发送的消息数量")
                .register(meterRegistry);
        this.messagesReceived = Counter.builder("rabbitmq.messages.received")
                .description("接收的消息数量")
                .register(meterRegistry);
        this.messageProcessingTime = Timer.builder("rabbitmq.message.processing.time")
                .description("消息处理时间")
                .register(meterRegistry);
    }
    
    public void recordMessageSent() {
        messagesSent.increment();
    }
    
    public void recordMessageReceived() {
        messagesReceived.increment();
    }
    
    public Timer.Sample startTimer() {
        return Timer.start(meterRegistry);
    }
}
```

### 3. 告警配置
```yaml
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 自定义告警规则
rabbitmq:
  alert:
    queue-length-threshold: 1000  # 队列长度告警阈值
    message-age-threshold: 300    # 消息年龄告警阈值（秒）
    consumer-count-threshold: 1   # 消费者数量告警阈值
```

## 最佳实践

### 1. 消息设计原则
- 消息体尽量小，避免传输大对象
- 消息格式统一，便于序列化和反序列化
- 包含必要的元数据（消息ID、时间戳等）
- 支持消息版本控制

### 2. 错误处理策略
- 实现重试机制，但要设置最大重试次数
- 使用死信队列处理无法处理的消息
- 记录详细的错误日志
- 实现消息补偿机制

### 3. 性能优化建议
- 合理设置预取数量
- 使用批量处理提高吞吐量
- 避免长时间占用连接
- 定期清理过期消息

### 4. 安全考虑
- 使用虚拟主机隔离不同环境
- 设置合适的用户权限
- 启用SSL/TLS加密传输
- 定期更新密码和证书

这个学习指南涵盖了RabbitMQ的核心概念、实际应用和最佳实践，结合项目中的代码示例，可以帮助你深入理解和掌握RabbitMQ的使用。
