#!/bin/bash

# Spring Cloud RabbitMQ 学习项目启动脚本

echo "========================================"
echo "Spring Cloud RabbitMQ 学习项目启动脚本"
echo "========================================"
echo

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        echo "错误: 未找到Java环境，请确保已安装JDK 17+"
        exit 1
    fi
    echo "✅ Java环境检查通过"
}

# 检查Maven环境
check_maven() {
    if ! command -v mvn &> /dev/null; then
        echo "错误: 未找到Maven环境，请确保已安装Maven 3.6+"
        exit 1
    fi
    echo "✅ Maven环境检查通过"
}

# 检查Docker环境
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "错误: 未找到Docker环境，请确保已安装Docker"
        exit 1
    fi
    if ! command -v docker-compose &> /dev/null; then
        echo "错误: 未找到Docker Compose，请确保已安装Docker Compose"
        exit 1
    fi
    echo "✅ Docker环境检查通过"
}

# 启动基础服务
start_infrastructure() {
    echo
    echo "========================================"
    echo "启动基础服务..."
    echo "========================================"
    echo "正在启动 MySQL, Redis, RabbitMQ..."
    
    docker-compose up -d mysql redis rabbitmq
    if [ $? -ne 0 ]; then
        echo "错误: Docker服务启动失败，请检查Docker是否正常运行"
        exit 1
    fi
    
    echo
    echo "基础服务启动完成！"
    echo "MySQL: localhost:3306 (root/123456)"
    echo "Redis: localhost:6379"
    echo "RabbitMQ: localhost:15672 (admin/admin123)"
    echo
    echo "等待服务完全启动..."
    sleep 30
}

# 编译项目
compile_project() {
    echo
    echo "========================================"
    echo "编译项目..."
    echo "========================================"
    echo "正在编译整个项目..."
    
    mvn clean compile -DskipTests
    if [ $? -ne 0 ]; then
        echo "错误: 项目编译失败"
        exit 1
    fi
    echo "✅ 项目编译完成！"
}

# 启动微服务
start_services() {
    echo
    echo "========================================"
    echo "启动微服务..."
    echo "========================================"
    echo "正在启动各个微服务，请稍候..."

    echo "启动网关服务 (端口: 8080)..."
    cd gateway-service && nohup mvn spring-boot:run > ../logs/gateway.log 2>&1 &
    cd ..
    sleep 10

    echo "启动生产者服务 (端口: 8081)..."
    cd producer-service && nohup mvn spring-boot:run > ../logs/producer.log 2>&1 &
    cd ..
    sleep 5

    echo "启动消费者服务 (端口: 8082)..."
    cd consumer-service && nohup mvn spring-boot:run > ../logs/consumer.log 2>&1 &
    cd ..
    sleep 5

    echo "启动订单服务 (端口: 8083)..."
    cd order-service && nohup mvn spring-boot:run > ../logs/order.log 2>&1 &
    cd ..
    sleep 5

    echo "启动库存服务 (端口: 8084)..."
    cd inventory-service && nohup mvn spring-boot:run > ../logs/inventory.log 2>&1 &
    cd ..

    echo
    echo "所有微服务正在启动中..."
    echo "请等待约2-3分钟让所有服务完全启动"
    echo
    echo "服务访问地址:"
    echo "网关服务: http://localhost:8080"
    echo "生产者服务: http://localhost:8081/doc.html"
    echo "消费者服务: http://localhost:8082/doc.html"
    echo "订单服务: http://localhost:8083/doc.html"
    echo "库存服务: http://localhost:8084/doc.html"
    echo
    echo "日志文件位置:"
    echo "网关服务: logs/gateway.log"
    echo "生产者服务: logs/producer.log"
    echo "消费者服务: logs/consumer.log"
    echo "订单服务: logs/order.log"
    echo "库存服务: logs/inventory.log"
}

# 完整启动
full_start() {
    echo
    echo "========================================"
    echo "完整启动流程..."
    echo "========================================"

    echo "步骤 1/3: 启动基础服务..."
    start_infrastructure

    echo "步骤 2/3: 编译项目..."
    compile_project

    echo "步骤 3/3: 启动微服务..."
    # 创建日志目录
    mkdir -p logs
    start_services

    echo
    echo "========================================"
    echo "🎉 完整启动流程完成！"
    echo "========================================"
    echo
    echo "📋 服务状态检查:"
    echo "基础服务: ✅ MySQL, Redis, RabbitMQ"
    echo "微服务: 🔄 正在启动中 (预计2-3分钟)"
    echo
    echo "🌐 访问地址:"
    echo "网关服务: http://localhost:8080"
    echo "API文档: http://localhost:8081/doc.html"
    echo "RabbitMQ管理: http://localhost:15672 (admin/admin123)"
    echo
    echo "💡 使用提示:"
    echo "1. 等待2-3分钟让所有服务完全启动"
    echo "2. 访问API文档查看接口说明"
    echo "3. 通过网关访问各个服务"
    echo "4. 查看RabbitMQ管理界面监控消息"
    echo "5. 查看logs目录下的日志文件"
}

# 停止服务
stop_services() {
    echo
    echo "========================================"
    echo "停止所有服务..."
    echo "========================================"
    
    echo "正在停止Docker服务..."
    docker-compose down
    
    echo "正在停止Java进程..."
    pkill -f "spring-boot:run" 2>/dev/null || true
    
    echo "✅ 所有服务已停止！"
}

# 检查服务状态
check_status() {
    echo
    echo "========================================"
    echo "检查服务状态..."
    echo "========================================"
    
    echo "Docker服务状态:"
    docker-compose ps
    
    echo
    echo "Java进程状态:"
    ps aux | grep "spring-boot:run" | grep -v grep || echo "没有运行的Java服务"
    
    echo
    echo "端口占用情况:"
    for port in 8080 8081 8082 8083 8084 3306 6379 5672 15672; do
        if lsof -i :$port >/dev/null 2>&1; then
            echo "端口 $port: ✅ 已占用"
        else
            echo "端口 $port: ❌ 未占用"
        fi
    done
}

# 主菜单
show_menu() {
    echo
    echo "选择启动模式:"
    echo "1. 启动基础服务 (MySQL, Redis, RabbitMQ)"
    echo "2. 编译项目"
    echo "3. 启动所有微服务"
    echo "4. 完整启动 (基础服务 + 编译 + 微服务)"
    echo "5. 停止所有服务"
    echo "6. 检查服务状态"
    echo "0. 退出"
    echo
    read -p "请输入选择 (0-6): " choice
}

# 主程序
main() {
    # 环境检查
    check_java
    check_maven
    check_docker
    
    while true; do
        show_menu
        
        case $choice in
            1)
                start_infrastructure
                ;;
            2)
                compile_project
                ;;
            3)
                mkdir -p logs
                start_services
                ;;
            4)
                full_start
                ;;
            5)
                stop_services
                ;;
            6)
                check_status
                ;;
            0)
                echo "退出脚本"
                exit 0
                ;;
            *)
                echo "无效选择，请重新输入"
                ;;
        esac
        
        echo
        read -p "按回车键继续..."
    done
}

# 运行主程序
main
