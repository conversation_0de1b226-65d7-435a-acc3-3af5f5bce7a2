version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: mysql-server
    hostname: mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: rabbitmq_learning
      MYSQL_USER: rabbitmq
      MYSQL_PASSWORD: rabbitmq123
      TZ: Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: redis-server
    hostname: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    networks:
      - app-network
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: rabbitmq-server
    hostname: rabbitmq
    ports:
      - "5672:5672"    # AMQP port
      - "15672:15672"  # Management UI port
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
      RABBITMQ_DEFAULT_VHOST: /
      TZ: Asia/Shanghai
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./docker/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./docker/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
      - ./docker/rabbitmq/enabled_plugins:/etc/rabbitmq/enabled_plugins
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
