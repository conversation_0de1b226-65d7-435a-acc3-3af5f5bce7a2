version: "3.4"
name: record

networks:
  app-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

services:

  es:
    image: "davyinsa/elasticsearch-ik:7.12.1"
    restart: ${RESTART:-always}
    networks:
      - app-network
    environment:
      - "discovery.type=single-node"
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "19202:9200"
    volumes:
      - ./esdata:/usr/share/elasticsearch/data

  mysql:
    image: "mysql:8.0.23"
    restart: ${RESTART:-always}
    networks:
      - app-network
    command: --init-connect='SET NAMES utf8' --lower_case_table_names=1 --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --max-connections=1200 --max-user-connections=1000 --innodb_file_per_table=on
    ports:
      - "33067:3306"
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYS<PERSON>_DATABASE: "ry"
      TZ: "Asia/Shanghai"
    volumes:
      - ./init-data:/docker-entrypoint-initdb.d
      - ./dbdata:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      timeout: 20s
      retries: 10

  redis:
    image: "redis:6-alpine"
    restart: ${RESTART:-always}
    networks:
      - app-network
    ports:
      - "6481:6379"

  server:
    image: registry.cn-hangzhou.aliyuncs.com/pubxinda/format-convert-jdk:8-jdk
    restart: ${RESTART:-always}
    networks:
      - app-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
      es:
        condition: service_started
    environment:
      - TZ=Asia/Shanghai
      - RUOYI_PROFILE=/files
      - SERVER_PORT=8080
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - FORMAT_OFFICEHOME=/usr/lib/libreoffice
      - EASYES_ADDRESS=es:9200
      - "SPRING_DATASOURCE_DRUID_MASTER_URL=*************************************************************************************************"
    volumes:
      - "./server:/app"
      - "/home/<USER>/files"
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "9190:8080"
    working_dir: /app
    command: [ "java","-jar","ruoyi-admin-x86_64.jar" ]

  web:
    image: caddy:2.8.4
    restart: ${RESTART:-always}
    networks:
      - app-network
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./client/Caddyfile:/etc/caddy/Caddyfile
      - ./client/dist:/usr/share/caddy
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "80:8080"