package com.rabbitmq.learning.producer.controller;

import com.rabbitmq.learning.common.message.InventoryMessage;
import com.rabbitmq.learning.common.message.OrderMessage;
import com.rabbitmq.learning.common.result.Result;
import com.rabbitmq.learning.producer.service.MessageProducerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 消息生产者控制器
 * 提供HTTP接口来演示各种RabbitMQ消息发送模式
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/producer")
@Tag(name = "消息生产者", description = "演示RabbitMQ各种消息发送模式的API接口")
public class MessageProducerController {

    @Autowired
    private MessageProducerService messageProducerService;

    // ==================== 基础消息模式 ====================

    /**
     * 发送简单消息
     * 演示最基本的点对点消息发送
     */
    @PostMapping("/simple")
    @Operation(summary = "发送简单消息", description = "演示最基本的点对点消息发送模式")
    public Result<String> sendSimpleMessage(
            @Parameter(description = "消息内容", required = true)
            @RequestParam String message) {
        try {
            messageProducerService.sendSimpleMessage(message);
            return Result.success("简单消息发送成功");
        } catch (Exception e) {
            log.error("发送简单消息失败: {}", e.getMessage(), e);
            return Result.error("发送简单消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送工作队列消息
     * 演示多个消费者竞争消费消息的工作队列模式
     */
    @PostMapping("/work-queue")
    @Operation(summary = "发送工作队列消息", description = "演示多个消费者竞争消费消息的工作队列模式")
    public Result<String> sendWorkQueueMessage(
            @Parameter(description = "任务内容", required = true)
            @RequestParam String taskContent,
            @Parameter(description = "任务ID", required = true)
            @RequestParam String taskId) {
        try {
            messageProducerService.sendWorkQueueMessage(taskContent, taskId);
            return Result.success("工作队列消息发送成功");
        } catch (Exception e) {
            log.error("发送工作队列消息失败: {}", e.getMessage(), e);
            return Result.error("发送工作队列消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送发布订阅消息
     * 演示一条消息被多个队列接收的发布订阅模式
     */
    @PostMapping("/publish-subscribe")
    @Operation(summary = "发送发布订阅消息", description = "演示一条消息被多个队列接收的发布订阅模式")
    public Result<String> sendPublishSubscribeMessage(
            @Parameter(description = "消息内容", required = true)
            @RequestParam String message) {
        try {
            messageProducerService.sendPublishSubscribeMessage(message);
            return Result.success("发布订阅消息发送成功");
        } catch (Exception e) {
            log.error("发送发布订阅消息失败: {}", e.getMessage(), e);
            return Result.error("发送发布订阅消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送路由消息
     * 演示根据路由键精确匹配队列的路由模式
     */
    @PostMapping("/routing")
    @Operation(summary = "发送路由消息", description = "演示根据路由键精确匹配队列的路由模式")
    public Result<String> sendRoutingMessage(
            @Parameter(description = "路由键", required = true)
            @RequestParam String routingKey,
            @Parameter(description = "消息内容", required = true)
            @RequestParam String message) {
        try {
            messageProducerService.sendRoutingMessage(routingKey, message);
            return Result.success("路由消息发送成功");
        } catch (Exception e) {
            log.error("发送路由消息失败: {}", e.getMessage(), e);
            return Result.error("发送路由消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送主题消息
     * 演示根据主题模式匹配队列的主题模式
     */
    @PostMapping("/topic")
    @Operation(summary = "发送主题消息", description = "演示根据主题模式匹配队列的主题模式")
    public Result<String> sendTopicMessage(
            @Parameter(description = "主题", required = true)
            @RequestParam String topic,
            @Parameter(description = "消息内容", required = true)
            @RequestParam String message) {
        try {
            messageProducerService.sendTopicMessage(topic, message);
            return Result.success("主题消息发送成功");
        } catch (Exception e) {
            log.error("发送主题消息失败: {}", e.getMessage(), e);
            return Result.error("发送主题消息失败: " + e.getMessage());
        }
    }

    // ==================== 订单业务消息 ====================

    /**
     * 发送订单创建消息
     * 演示实际业务场景中的订单创建消息
     */
    @PostMapping("/order/create")
    @Operation(summary = "发送订单创建消息", description = "演示实际业务场景中的订单创建消息")
    public Result<String> sendOrderCreateMessage(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo,
            @Parameter(description = "用户ID", required = true)
            @RequestParam Long userId,
            @Parameter(description = "商品ID", required = true)
            @RequestParam Long productId,
            @Parameter(description = "商品名称", required = true)
            @RequestParam String productName,
            @Parameter(description = "购买数量", required = true)
            @RequestParam Integer quantity,
            @Parameter(description = "单价", required = true)
            @RequestParam BigDecimal price) {
        try {
            OrderMessage orderMessage = new OrderMessage(orderNo, userId);
            orderMessage.setProductId(productId);
            orderMessage.setProductName(productName);
            orderMessage.setQuantity(quantity);
            orderMessage.setPrice(price);
            orderMessage.setTotalAmount(price.multiply(BigDecimal.valueOf(quantity)));
            
            messageProducerService.sendOrderCreateMessage(orderMessage);
            return Result.success("订单创建消息发送成功");
        } catch (Exception e) {
            log.error("发送订单创建消息失败: {}", e.getMessage(), e);
            return Result.error("发送订单创建消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送订单支付消息
     * 演示订单支付成功后的消息通知
     */
    @PostMapping("/order/payment")
    @Operation(summary = "发送订单支付消息", description = "演示订单支付成功后的消息通知")
    public Result<String> sendOrderPaymentMessage(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo,
            @Parameter(description = "用户ID", required = true)
            @RequestParam Long userId,
            @Parameter(description = "支付金额", required = true)
            @RequestParam BigDecimal totalAmount,
            @Parameter(description = "支付方式", required = true)
            @RequestParam Integer paymentMethod) {
        try {
            OrderMessage orderMessage = new OrderMessage(orderNo, userId);
            orderMessage.setTotalAmount(totalAmount);
            orderMessage.setPaymentMethod(paymentMethod);
            
            messageProducerService.sendOrderPaymentMessage(orderMessage);
            return Result.success("订单支付消息发送成功");
        } catch (Exception e) {
            log.error("发送订单支付消息失败: {}", e.getMessage(), e);
            return Result.error("发送订单支付消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送订单取消消息
     * 演示订单取消时的消息通知
     */
    @PostMapping("/order/cancel")
    @Operation(summary = "发送订单取消消息", description = "演示订单取消时的消息通知")
    public Result<String> sendOrderCancelMessage(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo,
            @Parameter(description = "用户ID", required = true)
            @RequestParam Long userId,
            @Parameter(description = "取消原因")
            @RequestParam(required = false) String reason) {
        try {
            OrderMessage orderMessage = new OrderMessage(orderNo, userId);
            orderMessage.setRemark(reason);
            
            messageProducerService.sendOrderCancelMessage(orderMessage);
            return Result.success("订单取消消息发送成功");
        } catch (Exception e) {
            log.error("发送订单取消消息失败: {}", e.getMessage(), e);
            return Result.error("发送订单取消消息失败: " + e.getMessage());
        }
    }

    // ==================== 库存业务消息 ====================

    /**
     * 发送库存锁定消息
     * 演示下单时锁定库存的消息
     */
    @PostMapping("/inventory/lock")
    @Operation(summary = "发送库存锁定消息", description = "演示下单时锁定库存的消息")
    public Result<String> sendInventoryLockMessage(
            @Parameter(description = "商品ID", required = true)
            @RequestParam Long productId,
            @Parameter(description = "锁定数量", required = true)
            @RequestParam Integer quantity,
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo) {
        try {
            InventoryMessage inventoryMessage = new InventoryMessage(productId, quantity, 
                    InventoryMessage.OperationType.LOCK, orderNo);
            
            messageProducerService.sendInventoryLockMessage(inventoryMessage);
            return Result.success("库存锁定消息发送成功");
        } catch (Exception e) {
            log.error("发送库存锁定消息失败: {}", e.getMessage(), e);
            return Result.error("发送库存锁定消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送库存解锁消息
     * 演示订单取消时解锁库存的消息
     */
    @PostMapping("/inventory/unlock")
    @Operation(summary = "发送库存解锁消息", description = "演示订单取消时解锁库存的消息")
    public Result<String> sendInventoryUnlockMessage(
            @Parameter(description = "商品ID", required = true)
            @RequestParam Long productId,
            @Parameter(description = "解锁数量", required = true)
            @RequestParam Integer quantity,
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo) {
        try {
            InventoryMessage inventoryMessage = new InventoryMessage(productId, quantity, 
                    InventoryMessage.OperationType.UNLOCK, orderNo);
            
            messageProducerService.sendInventoryUnlockMessage(inventoryMessage);
            return Result.success("库存解锁消息发送成功");
        } catch (Exception e) {
            log.error("发送库存解锁消息失败: {}", e.getMessage(), e);
            return Result.error("发送库存解锁消息失败: " + e.getMessage());
        }
    }

    // ==================== 延时消息 ====================

    /**
     * 发送延时消息
     * 演示延时消息的使用，如订单超时自动取消
     */
    @PostMapping("/delay")
    @Operation(summary = "发送延时消息", description = "演示延时消息的使用，如订单超时自动取消")
    public Result<String> sendDelayMessage(
            @Parameter(description = "消息内容", required = true)
            @RequestParam String message,
            @Parameter(description = "延时时间（秒）", required = true)
            @RequestParam Long delaySeconds) {
        try {
            messageProducerService.sendOrderTimeoutCheckMessage(message, (int) (delaySeconds / 60));
            return Result.success("延时消息发送成功，将在" + delaySeconds + "秒后处理");
        } catch (Exception e) {
            log.error("发送延时消息失败: {}", e.getMessage(), e);
            return Result.error("发送延时消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送订单超时检查消息
     * 演示订单超时自动取消的延时消息
     */
    @PostMapping("/order/timeout-check")
    @Operation(summary = "发送订单超时检查消息", description = "演示订单超时自动取消的延时消息")
    public Result<String> sendOrderTimeoutCheckMessage(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo,
            @Parameter(description = "延时分钟数", required = true)
            @RequestParam Integer delayMinutes) {
        try {
            messageProducerService.sendOrderTimeoutCheckMessage(orderNo, delayMinutes);
            return Result.success("订单超时检查消息发送成功，将在" + delayMinutes + "分钟后检查订单状态");
        } catch (Exception e) {
            log.error("发送订单超时检查消息失败: {}", e.getMessage(), e);
            return Result.error("发送订单超时检查消息失败: " + e.getMessage());
        }
    }

    // ==================== 优先级消息 ====================

    /**
     * 发送优先级消息
     * 演示消息优先级的使用
     */
    @PostMapping("/priority")
    @Operation(summary = "发送优先级消息", description = "演示消息优先级的使用，优先级高的消息会被优先消费")
    public Result<String> sendPriorityMessage(
            @Parameter(description = "消息内容", required = true)
            @RequestParam String message,
            @Parameter(description = "优先级 (0-10，数字越大优先级越高)", required = true)
            @RequestParam Integer priority) {
        try {
            if (priority < 0 || priority > 10) {
                return Result.error("优先级必须在0-10之间");
            }

            messageProducerService.sendPriorityMessage(message, priority);
            return Result.success("优先级消息发送成功，优先级: " + priority);
        } catch (Exception e) {
            log.error("发送优先级消息失败: {}", e.getMessage(), e);
            return Result.error("发送优先级消息失败: " + e.getMessage());
        }
    }

    // ==================== 批量消息 ====================

    /**
     * 批量发送消息
     * 演示批量发送消息的性能优化
     */
    @PostMapping("/batch")
    @Operation(summary = "批量发送消息", description = "演示批量发送消息的性能优化")
    public Result<String> sendBatchMessages(
            @Parameter(description = "消息前缀", required = true)
            @RequestParam String messagePrefix,
            @Parameter(description = "消息数量", required = true)
            @RequestParam Integer count) {
        try {
            if (count <= 0 || count > 1000) {
                return Result.error("消息数量必须在1-1000之间");
            }

            for (int i = 1; i <= count; i++) {
                String message = messagePrefix + " - " + i;
                messageProducerService.sendSimpleMessage(message);
            }

            return Result.success("批量发送" + count + "条消息成功");
        } catch (Exception e) {
            log.error("批量发送消息失败: {}", e.getMessage(), e);
            return Result.error("批量发送消息失败: " + e.getMessage());
        }
    }

    // ==================== 测试接口 ====================

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查生产者服务是否正常运行")
    public Result<String> health() {
        return Result.success("生产者服务运行正常");
    }

    /**
     * 获取服务信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取服务信息", description = "获取生产者服务的基本信息")
    public Result<Object> info() {
        return Result.success("RabbitMQ消息生产者服务 - 用于演示各种消息发送模式");
    }
}
