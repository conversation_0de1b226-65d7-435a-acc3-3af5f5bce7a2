package com.rabbitmq.learning.consumer.listener;

import com.rabbitmq.client.Channel;
import com.rabbitmq.learning.common.constant.RabbitMQConstant;
import com.rabbitmq.learning.common.message.InventoryMessage;
import com.rabbitmq.learning.common.util.RabbitMQUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 库存消息监听器
 * 处理库存相关的消息，演示分布式事务和库存管理场景
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@Component
public class InventoryMessageListener {

    @Autowired
    private RabbitMQUtil rabbitMQUtil;

    /**
     * 处理库存锁定消息
     * 当需要锁定库存时触发（如下单时）
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.INVENTORY_LOCK)
    public void handleInventoryLockMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析库存消息
            InventoryMessage inventoryMessage = rabbitMQUtil.parseMessage(message, InventoryMessage.class);
            
            log.info("接收到库存锁定消息 - ProductId: {}, Quantity: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getQuantity(), 
                    inventoryMessage.getOrderNo());
            
            // 处理库存锁定业务逻辑
            processInventoryLock(inventoryMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("库存锁定消息处理完成 - ProductId: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("处理库存锁定消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "库存锁定");
        }
    }

    /**
     * 处理库存解锁消息
     * 当需要解锁库存时触发（如订单取消时）
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.INVENTORY_UNLOCK)
    public void handleInventoryUnlockMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析库存消息
            InventoryMessage inventoryMessage = rabbitMQUtil.parseMessage(message, InventoryMessage.class);
            
            log.info("接收到库存解锁消息 - ProductId: {}, Quantity: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getQuantity(), 
                    inventoryMessage.getOrderNo());
            
            // 处理库存解锁业务逻辑
            processInventoryUnlock(inventoryMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("库存解锁消息处理完成 - ProductId: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("处理库存解锁消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "库存解锁");
        }
    }

    /**
     * 处理库存扣减消息
     * 当需要扣减库存时触发（如支付成功后）
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.INVENTORY_DEDUCT)
    public void handleInventoryDeductMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析库存消息
            InventoryMessage inventoryMessage = rabbitMQUtil.parseMessage(message, InventoryMessage.class);
            
            log.info("接收到库存扣减消息 - ProductId: {}, Quantity: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getQuantity(), 
                    inventoryMessage.getOrderNo());
            
            // 处理库存扣减业务逻辑
            processInventoryDeduct(inventoryMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("库存扣减消息处理完成 - ProductId: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("处理库存扣减消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "库存扣减");
        }
    }

    /**
     * 处理库存同步消息
     * 当需要同步库存信息时触发
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.INVENTORY_SYNC)
    public void handleInventorySyncMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析库存消息
            InventoryMessage inventoryMessage = rabbitMQUtil.parseMessage(message, InventoryMessage.class);
            
            log.info("接收到库存同步消息 - ProductId: {}, Quantity: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getQuantity());
            
            // 处理库存同步业务逻辑
            processInventorySync(inventoryMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("库存同步消息处理完成 - ProductId: {}", inventoryMessage.getProductId());
            
        } catch (Exception e) {
            log.error("处理库存同步消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "库存同步");
        }
    }

    /**
     * 处理库存预警消息
     * 当库存低于安全阈值时触发
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.INVENTORY_ALERT)
    public void handleInventoryAlertMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析库存消息
            InventoryMessage inventoryMessage = rabbitMQUtil.parseMessage(message, InventoryMessage.class);
            
            log.info("接收到库存预警消息 - ProductId: {}, CurrentQuantity: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getQuantity());
            
            // 处理库存预警业务逻辑
            processInventoryAlert(inventoryMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("库存预警消息处理完成 - ProductId: {}", inventoryMessage.getProductId());
            
        } catch (Exception e) {
            log.error("处理库存预警消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "库存预警");
        }
    }

    // ==================== 业务处理方法 ====================

    /**
     * 处理库存锁定业务逻辑
     * 
     * @param inventoryMessage 库存消息
     */
    private void processInventoryLock(InventoryMessage inventoryMessage) {
        log.info("开始处理库存锁定业务 - ProductId: {}, OrderNo: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
        
        try {
            // 1. 检查商品是否存在
            validateProduct(inventoryMessage.getProductId());
            
            // 2. 检查可用库存是否充足
            checkAvailableInventory(inventoryMessage);
            
            // 3. 锁定库存（减少可用库存，增加锁定库存）
            lockInventoryQuantity(inventoryMessage);
            
            // 4. 记录库存变更日志
            recordInventoryChange(inventoryMessage, "LOCK");
            
            // 5. 检查是否需要库存预警
            checkInventoryAlert(inventoryMessage.getProductId());
            
            log.info("库存锁定业务处理完成 - ProductId: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("库存锁定业务处理失败 - ProductId: {}, OrderNo: {}, Error: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理库存解锁业务逻辑
     * 
     * @param inventoryMessage 库存消息
     */
    private void processInventoryUnlock(InventoryMessage inventoryMessage) {
        log.info("开始处理库存解锁业务 - ProductId: {}, OrderNo: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
        
        try {
            // 1. 检查锁定库存是否存在
            validateLockedInventory(inventoryMessage);
            
            // 2. 解锁库存（减少锁定库存，增加可用库存）
            unlockInventoryQuantity(inventoryMessage);
            
            // 3. 记录库存变更日志
            recordInventoryChange(inventoryMessage, "UNLOCK");
            
            log.info("库存解锁业务处理完成 - ProductId: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("库存解锁业务处理失败 - ProductId: {}, OrderNo: {}, Error: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理库存扣减业务逻辑
     * 
     * @param inventoryMessage 库存消息
     */
    private void processInventoryDeduct(InventoryMessage inventoryMessage) {
        log.info("开始处理库存扣减业务 - ProductId: {}, OrderNo: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
        
        try {
            // 1. 检查锁定库存是否存在
            validateLockedInventory(inventoryMessage);
            
            // 2. 扣减库存（减少锁定库存和总库存，增加已售库存）
            deductInventoryQuantity(inventoryMessage);
            
            // 3. 记录库存变更日志
            recordInventoryChange(inventoryMessage, "DEDUCT");
            
            // 4. 检查是否需要库存预警
            checkInventoryAlert(inventoryMessage.getProductId());
            
            log.info("库存扣减业务处理完成 - ProductId: {}, OrderNo: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("库存扣减业务处理失败 - ProductId: {}, OrderNo: {}, Error: {}", 
                    inventoryMessage.getProductId(), inventoryMessage.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理库存同步业务逻辑
     * 
     * @param inventoryMessage 库存消息
     */
    private void processInventorySync(InventoryMessage inventoryMessage) {
        log.info("开始处理库存同步业务 - ProductId: {}", inventoryMessage.getProductId());
        
        try {
            // 1. 同步库存数据到缓存
            syncInventoryToCache(inventoryMessage);
            
            // 2. 同步库存数据到搜索引擎
            syncInventoryToSearchEngine(inventoryMessage);
            
            // 3. 同步库存数据到数据仓库
            syncInventoryToDataWarehouse(inventoryMessage);
            
            log.info("库存同步业务处理完成 - ProductId: {}", inventoryMessage.getProductId());
            
        } catch (Exception e) {
            log.error("库存同步业务处理失败 - ProductId: {}, Error: {}", 
                    inventoryMessage.getProductId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理库存预警业务逻辑
     * 
     * @param inventoryMessage 库存消息
     */
    private void processInventoryAlert(InventoryMessage inventoryMessage) {
        log.info("开始处理库存预警业务 - ProductId: {}", inventoryMessage.getProductId());
        
        try {
            // 1. 发送库存预警邮件
            sendInventoryAlertEmail(inventoryMessage);
            
            // 2. 发送库存预警短信
            sendInventoryAlertSms(inventoryMessage);
            
            // 3. 推送库存预警到管理后台
            pushInventoryAlertToAdmin(inventoryMessage);
            
            // 4. 记录预警日志
            recordInventoryAlert(inventoryMessage);
            
            log.info("库存预警业务处理完成 - ProductId: {}", inventoryMessage.getProductId());
            
        } catch (Exception e) {
            log.error("库存预警业务处理失败 - ProductId: {}, Error: {}", 
                    inventoryMessage.getProductId(), e.getMessage(), e);
            throw e;
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 处理消息错误
     * 
     * @param message AMQP消息
     * @param channel 通道
     * @param deliveryTag 投递标签
     * @param messageType 消息类型
     */
    private void handleMessageError(Message message, Channel channel, long deliveryTag, String messageType) {
        try {
            if (rabbitMQUtil.isExceedMaxRetry(message)) {
                log.error("{}消息重试次数超限，拒绝消息 - DeliveryTag: {}", messageType, deliveryTag);
                channel.basicReject(deliveryTag, false);
            } else {
                rabbitMQUtil.incrementRetryCount(message);
                log.info("{}消息处理失败，重新入队 - DeliveryTag: {}, RetryCount: {}", 
                        messageType, deliveryTag, rabbitMQUtil.getRetryCount(message));
                channel.basicReject(deliveryTag, true);
            }
        } catch (IOException ioException) {
            log.error("消息确认失败 - DeliveryTag: {}, Error: {}", deliveryTag, ioException.getMessage());
        }
    }

    // ==================== 模拟业务方法 ====================
    // 以下方法为模拟业务逻辑，实际项目中应该调用相应的服务

    private void validateProduct(Long productId) {
        log.info("验证商品是否存在 - ProductId: {}", productId);
    }

    private void checkAvailableInventory(InventoryMessage inventoryMessage) {
        log.info("检查可用库存 - ProductId: {}, RequiredQuantity: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getQuantity());
    }

    private void lockInventoryQuantity(InventoryMessage inventoryMessage) {
        log.info("锁定库存数量 - ProductId: {}, Quantity: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getQuantity());
    }

    private void validateLockedInventory(InventoryMessage inventoryMessage) {
        log.info("验证锁定库存 - ProductId: {}, OrderNo: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getOrderNo());
    }

    private void unlockInventoryQuantity(InventoryMessage inventoryMessage) {
        log.info("解锁库存数量 - ProductId: {}, Quantity: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getQuantity());
    }

    private void deductInventoryQuantity(InventoryMessage inventoryMessage) {
        log.info("扣减库存数量 - ProductId: {}, Quantity: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getQuantity());
    }

    private void recordInventoryChange(InventoryMessage inventoryMessage, String operation) {
        log.info("记录库存变更日志 - ProductId: {}, Operation: {}, Quantity: {}", 
                inventoryMessage.getProductId(), operation, inventoryMessage.getQuantity());
    }

    private void checkInventoryAlert(Long productId) {
        log.info("检查库存预警 - ProductId: {}", productId);
    }

    private void syncInventoryToCache(InventoryMessage inventoryMessage) {
        log.info("同步库存到缓存 - ProductId: {}", inventoryMessage.getProductId());
    }

    private void syncInventoryToSearchEngine(InventoryMessage inventoryMessage) {
        log.info("同步库存到搜索引擎 - ProductId: {}", inventoryMessage.getProductId());
    }

    private void syncInventoryToDataWarehouse(InventoryMessage inventoryMessage) {
        log.info("同步库存到数据仓库 - ProductId: {}", inventoryMessage.getProductId());
    }

    private void sendInventoryAlertEmail(InventoryMessage inventoryMessage) {
        log.info("发送库存预警邮件 - ProductId: {}", inventoryMessage.getProductId());
    }

    private void sendInventoryAlertSms(InventoryMessage inventoryMessage) {
        log.info("发送库存预警短信 - ProductId: {}", inventoryMessage.getProductId());
    }

    private void pushInventoryAlertToAdmin(InventoryMessage inventoryMessage) {
        log.info("推送库存预警到管理后台 - ProductId: {}", inventoryMessage.getProductId());
    }

    private void recordInventoryAlert(InventoryMessage inventoryMessage) {
        log.info("记录库存预警日志 - ProductId: {}", inventoryMessage.getProductId());
    }
}
