# 网关服务配置文件
server:
  port: 8080

spring:
  application:
    name: gateway-service
  
  # Spring Cloud Gateway配置
  cloud:
    gateway:
      # 路由配置
      routes:
        # 生产者服务路由
        - id: producer-service
          uri: http://localhost:8081
          predicates:
            - Path=/producer/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                redis-rate-limiter.requestedTokens: 1
        
        # 消费者服务路由
        - id: consumer-service
          uri: http://localhost:8082
          predicates:
            - Path=/consumer/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                redis-rate-limiter.requestedTokens: 1
        
        # 订单服务路由
        - id: order-service
          uri: http://localhost:8083
          predicates:
            - Path=/order/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 40
                redis-rate-limiter.requestedTokens: 1
        
        # 库存服务路由
        - id: inventory-service
          uri: http://localhost:8084
          predicates:
            - Path=/inventory/**
          filters:
            - StripPrefix=1
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 40
                redis-rate-limiter.requestedTokens: 1
      
      # 全局过滤器配置
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin
        - AddResponseHeader=X-Response-Default-Foo, Default-Bar
      
      # 全局跨域配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            exposedHeaders: "Content-Disposition,Content-Type,Cache-Control"

  # Redis配置（用于限流）
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 1
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# 日志配置
logging:
  level:
    org.springframework.cloud.gateway: debug
    org.springframework.web.reactive: debug
    reactor.netty: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true

# 自定义配置
gateway:
  # 限流配置
  rate-limit:
    # 是否启用限流
    enabled: true
    # 默认限流规则
    default:
      replenish-rate: 10
      burst-capacity: 20
      requested-tokens: 1
  
  # 跨域配置
  cors:
    # 是否启用跨域
    enabled: true
    # 允许的源
    allowed-origins: "*"
    # 允许的方法
    allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
    # 允许的头
    allowed-headers: "*"
    # 是否允许凭证
    allow-credentials: true
  
  # 请求日志配置
  request-log:
    # 是否启用请求日志
    enabled: true
    # 是否记录请求体
    include-request-body: false
    # 是否记录响应体
    include-response-body: false
