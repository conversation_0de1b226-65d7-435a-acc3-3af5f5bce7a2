package com.rabbitmq.learning.consumer.listener;

import com.alibaba.fastjson2.JSON;
import com.rabbitmq.client.Channel;
import com.rabbitmq.learning.common.constant.RabbitMQConstant;
import com.rabbitmq.learning.common.message.BaseMessage;
import com.rabbitmq.learning.common.util.RabbitMQUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 基础消息监听器
 * 演示RabbitMQ的基础消息消费模式
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@Component
public class BasicMessageListener {

    @Autowired
    private RabbitMQUtil rabbitMQUtil;

    /**
     * 简单队列消费者
     * 演示最基本的消息消费模式
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.WORK)
    public void handleSimpleMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析消息
            BaseMessage baseMessage = rabbitMQUtil.parseMessage(message, BaseMessage.class);
            
            log.info("接收到简单消息 - MessageId: {}, Content: {}", 
                    baseMessage.getMessageId(), baseMessage.getExtendInfo());
            
            // 模拟业务处理
            processSimpleMessage(baseMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            rabbitMQUtil.logMessageProcess(message, "简单消息消费", true, null);
            
        } catch (Exception e) {
            log.error("处理简单消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            
            try {
                // 检查重试次数
                if (rabbitMQUtil.isExceedMaxRetry(message)) {
                    log.error("消息重试次数超限，拒绝消息 - DeliveryTag: {}", deliveryTag);
                    // 拒绝消息，不重新入队
                    channel.basicReject(deliveryTag, false);
                } else {
                    // 增加重试次数
                    rabbitMQUtil.incrementRetryCount(message);
                    log.info("消息处理失败，重新入队 - DeliveryTag: {}, RetryCount: {}", 
                            deliveryTag, rabbitMQUtil.getRetryCount(message));
                    // 拒绝消息，重新入队
                    channel.basicReject(deliveryTag, true);
                }
            } catch (IOException ioException) {
                log.error("消息确认失败 - DeliveryTag: {}, Error: {}", deliveryTag, ioException.getMessage());
            }
            
            rabbitMQUtil.logMessageProcess(message, "简单消息消费", false, e.getMessage());
        }
    }

    /**
     * 工作队列消费者1
     * 演示多个消费者竞争消费同一队列的消息
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.WORK, containerFactory = "rabbitListenerContainerFactory")
    public void handleWorkQueueMessage1(Message message, Channel channel) {
        handleWorkQueueMessage(message, channel, "消费者1");
    }

    /**
     * 工作队列消费者2
     * 演示多个消费者竞争消费同一队列的消息
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.WORK, containerFactory = "rabbitListenerContainerFactory")
    public void handleWorkQueueMessage2(Message message, Channel channel) {
        handleWorkQueueMessage(message, channel, "消费者2");
    }

    /**
     * 处理工作队列消息的通用方法
     * 
     * @param message AMQP消息
     * @param channel 通道
     * @param consumerName 消费者名称
     */
    private void handleWorkQueueMessage(Message message, Channel channel, String consumerName) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析消息
            BaseMessage baseMessage = rabbitMQUtil.parseMessage(message, BaseMessage.class);
            
            log.info("{} 接收到工作队列消息 - MessageId: {}, Content: {}", 
                    consumerName, baseMessage.getMessageId(), baseMessage.getExtendInfo());
            
            // 模拟不同的处理时间
            if ("消费者1".equals(consumerName)) {
                Thread.sleep(1000); // 消费者1处理较慢
            } else {
                Thread.sleep(500);  // 消费者2处理较快
            }
            
            // 模拟业务处理
            processWorkQueueMessage(baseMessage, consumerName);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("{} 处理工作队列消息完成 - MessageId: {}", consumerName, baseMessage.getMessageId());
            
        } catch (Exception e) {
            log.error("{} 处理工作队列消息失败 - DeliveryTag: {}, Error: {}", 
                    consumerName, deliveryTag, e.getMessage(), e);
            
            try {
                // 拒绝消息，重新入队
                channel.basicReject(deliveryTag, true);
            } catch (IOException ioException) {
                log.error("消息确认失败 - DeliveryTag: {}, Error: {}", deliveryTag, ioException.getMessage());
            }
        }
    }

    /**
     * 发布订阅消费者1
     * 演示发布订阅模式，多个消费者都会收到同一条消息
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.PUBLISH_SUBSCRIBE_1)
    public void handlePublishSubscribeMessage1(Message message, Channel channel) {
        handlePublishSubscribeMessage(message, channel, "订阅者1");
    }

    /**
     * 发布订阅消费者2
     * 演示发布订阅模式，多个消费者都会收到同一条消息
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.PUBLISH_SUBSCRIBE_2)
    public void handlePublishSubscribeMessage2(Message message, Channel channel) {
        handlePublishSubscribeMessage(message, channel, "订阅者2");
    }

    /**
     * 处理发布订阅消息的通用方法
     * 
     * @param message AMQP消息
     * @param channel 通道
     * @param subscriberName 订阅者名称
     */
    private void handlePublishSubscribeMessage(Message message, Channel channel, String subscriberName) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析消息
            BaseMessage baseMessage = rabbitMQUtil.parseMessage(message, BaseMessage.class);
            
            log.info("{} 接收到发布订阅消息 - MessageId: {}, Content: {}", 
                    subscriberName, baseMessage.getMessageId(), baseMessage.getExtendInfo());
            
            // 模拟业务处理
            processPublishSubscribeMessage(baseMessage, subscriberName);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("{} 处理发布订阅消息完成 - MessageId: {}", subscriberName, baseMessage.getMessageId());
            
        } catch (Exception e) {
            log.error("{} 处理发布订阅消息失败 - DeliveryTag: {}, Error: {}", 
                    subscriberName, deliveryTag, e.getMessage(), e);
            
            try {
                // 拒绝消息，不重新入队（避免重复处理）
                channel.basicReject(deliveryTag, false);
            } catch (IOException ioException) {
                log.error("消息确认失败 - DeliveryTag: {}, Error: {}", deliveryTag, ioException.getMessage());
            }
        }
    }

    // ==================== 业务处理方法 ====================

    /**
     * 处理简单消息的业务逻辑
     * 
     * @param message 消息对象
     */
    private void processSimpleMessage(BaseMessage message) {
        // 模拟业务处理
        log.info("执行简单消息业务逻辑 - MessageId: {}", message.getMessageId());
        
        // 这里可以添加具体的业务逻辑
        // 例如：数据库操作、调用其他服务等
    }

    /**
     * 处理工作队列消息的业务逻辑
     * 
     * @param message 消息对象
     * @param consumerName 消费者名称
     */
    private void processWorkQueueMessage(BaseMessage message, String consumerName) {
        // 模拟业务处理
        log.info("{} 执行工作队列消息业务逻辑 - MessageId: {}", consumerName, message.getMessageId());
        
        // 这里可以添加具体的业务逻辑
        // 例如：任务处理、数据计算等
    }

    /**
     * 处理发布订阅消息的业务逻辑
     * 
     * @param message 消息对象
     * @param subscriberName 订阅者名称
     */
    private void processPublishSubscribeMessage(BaseMessage message, String subscriberName) {
        // 模拟业务处理
        log.info("{} 执行发布订阅消息业务逻辑 - MessageId: {}", subscriberName, message.getMessageId());
        
        // 这里可以添加具体的业务逻辑
        // 例如：通知处理、日志记录等
    }
}
