# Spring Cloud RabbitMQ 学习项目

## 项目简介

这是一个企业级的Spring Cloud项目，专门用于深度学习RabbitMQ的各种使用场景。项目采用微服务架构，包含多个服务模块，全面演示了RabbitMQ在实际业务场景中的应用。

## 项目特色

- 🚀 **企业级架构**: 采用Spring Cloud微服务架构
- 📚 **详细注释**: 每个类和方法都有详细的中文注释
- 🔄 **完整业务流程**: 模拟真实的订单和库存业务场景
- 🛠️ **多种消息模式**: 涵盖RabbitMQ的所有主要使用模式
- 📊 **监控完善**: 集成Actuator、Knife4j等监控工具
- 🐳 **容器化部署**: 提供完整的Docker配置

## 技术栈

### 核心框架
- **Spring Boot**: 3.2.0
- **Spring Cloud**: 2023.0.0
- **Spring Cloud Gateway**: 网关服务
- **Spring AMQP**: RabbitMQ集成

### 数据存储
- **MySQL**: 8.0 - 主数据库
- **Redis**: 7.0 - 缓存和分布式锁
- **MyBatis Plus**: 3.5.4 - ORM框架

### 消息队列
- **RabbitMQ**: 3.12 - 消息中间件

### 工具组件
- **Knife4j**: 4.3.0 - API文档
- **Druid**: 1.2.20 - 数据库连接池
- **Redisson**: 3.24.3 - 分布式锁
- **Hutool**: 5.8.22 - 工具类库
- **FastJSON2**: 2.0.43 - JSON处理

## 项目结构

```
springcloud-rabbitmq-learning/
├── common/                     # 公共模块
│   ├── entity/                # 实体类
│   ├── message/               # 消息类
│   ├── result/                # 响应结果类
│   ├── constant/              # 常量定义
│   └── util/                  # 工具类
├── producer-service/          # 消息生产者服务 (8081)
│   ├── controller/            # 控制器
│   ├── service/               # 业务服务
│   └── config/                # 配置类
├── consumer-service/          # 消息消费者服务 (8082)
│   ├── listener/              # 消息监听器
│   └── config/                # 配置类
├── order-service/             # 订单服务 (8083)
│   ├── controller/            # 订单控制器
│   ├── service/               # 订单业务服务
│   └── mapper/                # 数据访问层
├── inventory-service/         # 库存服务 (8084)
│   ├── controller/            # 库存控制器
│   ├── service/               # 库存业务服务
│   └── mapper/                # 数据访问层
├── gateway-service/           # 网关服务 (8080)
│   ├── filter/                # 网关过滤器
│   └── config/                # 网关配置
└── docker/                    # Docker配置文件
    ├── mysql/                 # MySQL配置
    ├── redis/                 # Redis配置
    └── rabbitmq/              # RabbitMQ配置
```

## RabbitMQ 学习内容

### 1. 基础消息模式

#### 简单队列模式 (Simple Queue)
- **场景**: 一对一消息传递
- **特点**: 一个生产者，一个消费者，一个队列
- **示例**: 发送简单文本消息

#### 工作队列模式 (Work Queue)
- **场景**: 任务分发，多个消费者竞争消费
- **特点**: 一个生产者，多个消费者，消息轮询分发
- **示例**: 批量任务处理

#### 发布订阅模式 (Publish/Subscribe)
- **场景**: 广播消息，所有订阅者都收到
- **特点**: 使用扇形交换机(fanout)，消息广播到所有绑定队列
- **示例**: 系统通知、日志收集

#### 路由模式 (Routing)
- **场景**: 根据路由键精确匹配
- **特点**: 使用直连交换机(direct)，根据routing key路由
- **示例**: 日志级别分类处理

#### 主题模式 (Topic)
- **场景**: 根据主题模式匹配
- **特点**: 使用主题交换机(topic)，支持通配符匹配
- **示例**: 按地区、类别等维度分发消息

### 2. 高级特性

#### 消息确认机制
- **生产者确认**: 确保消息到达交换机和队列
- **消费者确认**: 手动ACK，确保消息被正确处理
- **示例**: 订单支付确认

#### 消息持久化
- **交换机持久化**: 服务重启后交换机不丢失
- **队列持久化**: 服务重启后队列不丢失
- **消息持久化**: 服务重启后消息不丢失

#### 死信队列 (Dead Letter Queue)
- **场景**: 处理无法正常消费的消息
- **特点**: 消息过期、被拒绝、队列满时进入死信队列
- **示例**: 异常订单处理

#### 延时消息
- **场景**: 定时任务、延时处理
- **实现**: TTL + 死信队列 或 延时插件
- **示例**: 订单超时自动取消

#### 优先级队列
- **场景**: 重要消息优先处理
- **特点**: 消息带优先级，高优先级先消费
- **示例**: VIP用户订单优先处理

### 3. 实际业务场景

#### 订单业务流程
```
下单 -> 锁定库存 -> 等待支付 -> 扣减库存 -> 发货 -> 完成
  |                    |
  v                    v
超时取消 <----- 释放库存
```

#### 分布式事务
- **场景**: 订单和库存的数据一致性
- **方案**: 消息驱动的最终一致性
- **补偿机制**: 失败时的数据回滚

#### 消息幂等性
- **问题**: 重复消费导致的数据不一致
- **解决**: 消息去重、业务幂等设计
- **实现**: Redis缓存消息ID

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 7.0+
- RabbitMQ 3.12+

### 本地开发环境搭建

#### 1. 启动基础服务
```bash
# 使用Docker Compose启动基础服务
docker-compose up -d mysql redis rabbitmq
```

#### 2. 创建数据库
```sql
-- 连接MySQL，创建数据库
CREATE DATABASE rabbitmq_learning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 3. 编译项目
```bash
# 编译整个项目
mvn clean compile

# 或者编译并跳过测试
mvn clean compile -DskipTests
```

#### 4. 启动服务
```bash
# 按顺序启动各个服务
cd gateway-service && mvn spring-boot:run &
cd producer-service && mvn spring-boot:run &
cd consumer-service && mvn spring-boot:run &
cd order-service && mvn spring-boot:run &
cd inventory-service && mvn spring-boot:run &
```

### 服务访问地址

| 服务名称 | 端口 | 访问地址 | API文档 |
|---------|------|----------|---------|
| 网关服务 | 8080 | http://localhost:8080 | - |
| 生产者服务 | 8081 | http://localhost:8081 | http://localhost:8081/doc.html |
| 消费者服务 | 8082 | http://localhost:8082 | http://localhost:8082/doc.html |
| 订单服务 | 8083 | http://localhost:8083 | http://localhost:8083/doc.html |
| 库存服务 | 8084 | http://localhost:8084 | http://localhost:8084/doc.html |

### 中间件访问地址

| 服务名称 | 端口 | 访问地址 | 用户名/密码 |
|---------|------|----------|------------|
| RabbitMQ管理界面 | 15672 | http://localhost:15672 | admin/admin123 |
| MySQL数据库 | 3306 | localhost:3306 | root/123456 |
| Redis缓存 | 6379 | localhost:6379 | 无密码 |

## 使用示例

### 1. 发送简单消息
```bash
curl -X POST "http://localhost:8080/producer/api/producer/simple" \
     -d "message=Hello RabbitMQ"
```

### 2. 创建订单
```bash
curl -X POST "http://localhost:8080/order/api/order/create" \
     -d "userId=10001&productId=1001&productName=iPhone 15 Pro&quantity=1&price=8999.00"
```

### 3. 支付订单
```bash
curl -X POST "http://localhost:8080/order/api/order/pay" \
     -d "orderNo=ORD20240101001&paymentMethod=1"
```

### 4. 发送延时消息
```bash
curl -X POST "http://localhost:8080/producer/api/producer/delay" \
     -d "message=延时消息测试&delaySeconds=60"
```

## 学习路径建议

### 第一阶段：基础概念
1. 了解消息队列的基本概念
2. 学习RabbitMQ的核心组件
3. 掌握基本的消息发送和接收

### 第二阶段：消息模式
1. 简单队列模式
2. 工作队列模式  
3. 发布订阅模式
4. 路由模式
5. 主题模式

### 第三阶段：高级特性
1. 消息确认机制
2. 消息持久化
3. 死信队列
4. 延时消息
5. 优先级队列

### 第四阶段：实际应用
1. 分布式事务
2. 消息幂等性
3. 性能优化
4. 监控告警
5. 故障处理

## 常见问题

### Q1: 消息丢失怎么办？
**A**: 通过以下机制保证消息不丢失：
- 生产者确认机制
- 消息持久化
- 消费者手动ACK
- 死信队列兜底

### Q2: 如何保证消息顺序？
**A**: 
- 使用单个队列
- 单个消费者
- 或者按业务分区

### Q3: 如何处理重复消息？
**A**:
- 消息去重（Redis缓存消息ID）
- 业务幂等性设计
- 数据库唯一约束

### Q4: 如何监控RabbitMQ？
**A**:
- RabbitMQ管理界面
- Actuator健康检查
- 自定义监控指标
- 日志分析

## 贡献指南

欢迎提交Issue和Pull Request来完善这个项目！

### 提交规范
- 代码需要有详细的中文注释
- 提交信息使用中文描述
- 新增功能需要添加对应的测试用例
- 更新文档说明

## 许可证

本项目采用 [Apache 2.0](LICENSE) 许可证。

## 联系方式

如果你在学习过程中遇到问题，可以通过以下方式联系：

- 提交Issue: [GitHub Issues](https://github.com/rabbitmq-learning/issues)
- 邮箱: <EMAIL>

---

**Happy Learning! 🎉**
