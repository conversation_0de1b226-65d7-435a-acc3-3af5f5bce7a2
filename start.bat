@echo off
chcp 65001 >nul
echo ========================================
echo Spring Cloud RabbitMQ 学习项目启动脚本
echo ========================================
echo.

echo 正在检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK 17+
    pause
    exit /b 1
)

echo 正在检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请确保已安装Maven 3.6+
    pause
    exit /b 1
)

echo.
echo 选择启动模式:
echo 1. 启动基础服务 (MySQL, Redis, RabbitMQ)
echo 2. 编译项目
echo 3. 启动所有微服务
echo 4. 完整启动 (基础服务 + 编译 + 微服务)
echo 5. 停止所有服务
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto start_infrastructure
if "%choice%"=="2" goto compile_project
if "%choice%"=="3" goto start_services
if "%choice%"=="4" goto full_start
if "%choice%"=="5" goto stop_services
echo 无效选择，退出...
pause
exit /b 1

:start_infrastructure
echo.
echo ========================================
echo 启动基础服务...
echo ========================================
echo 正在启动 MySQL, Redis, RabbitMQ...
docker-compose up -d mysql redis rabbitmq
if %errorlevel% neq 0 (
    echo 错误: Docker服务启动失败，请检查Docker是否正常运行
    pause
    exit /b 1
)
echo.
echo 基础服务启动完成！
echo MySQL: localhost:3306 (root/123456)
echo Redis: localhost:6379
echo RabbitMQ: localhost:15672 (admin/admin123)
echo.
echo 等待服务完全启动...
timeout /t 30 /nobreak >nul
goto end

:compile_project
echo.
echo ========================================
echo 编译项目...
echo ========================================
echo 正在编译整个项目...
call mvn clean compile -DskipTests
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)
echo 项目编译完成！
goto end

:start_services
echo.
echo ========================================
echo 启动微服务...
echo ========================================
echo 正在启动各个微服务，请稍候...

echo 启动网关服务 (端口: 8080)...
start "Gateway Service" cmd /c "cd gateway-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo 启动生产者服务 (端口: 8081)...
start "Producer Service" cmd /c "cd producer-service && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo 启动消费者服务 (端口: 8082)...
start "Consumer Service" cmd /c "cd consumer-service && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo 启动订单服务 (端口: 8083)...
start "Order Service" cmd /c "cd order-service && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo 启动库存服务 (端口: 8084)...
start "Inventory Service" cmd /c "cd inventory-service && mvn spring-boot:run"

echo.
echo 所有微服务正在启动中...
echo 请等待约2-3分钟让所有服务完全启动
echo.
echo 服务访问地址:
echo 网关服务: http://localhost:8080
echo 生产者服务: http://localhost:8081/doc.html
echo 消费者服务: http://localhost:8082/doc.html
echo 订单服务: http://localhost:8083/doc.html
echo 库存服务: http://localhost:8084/doc.html
goto end

:full_start
echo.
echo ========================================
echo 完整启动流程...
echo ========================================

echo 步骤 1/3: 启动基础服务...
docker-compose up -d mysql redis rabbitmq
if %errorlevel% neq 0 (
    echo 错误: Docker服务启动失败
    pause
    exit /b 1
)
echo 等待基础服务启动...
timeout /t 30 /nobreak >nul

echo 步骤 2/3: 编译项目...
call mvn clean compile -DskipTests
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo 步骤 3/3: 启动微服务...
echo 启动网关服务...
start "Gateway Service" cmd /c "cd gateway-service && mvn spring-boot:run"
timeout /t 10 /nobreak >nul

echo 启动生产者服务...
start "Producer Service" cmd /c "cd producer-service && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo 启动消费者服务...
start "Consumer Service" cmd /c "cd consumer-service && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo 启动订单服务...
start "Order Service" cmd /c "cd order-service && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo 启动库存服务...
start "Inventory Service" cmd /c "cd inventory-service && mvn spring-boot:run"

echo.
echo ========================================
echo 🎉 完整启动流程完成！
echo ========================================
echo.
echo 📋 服务状态检查:
echo 基础服务: ✅ MySQL, Redis, RabbitMQ
echo 微服务: 🔄 正在启动中 (预计2-3分钟)
echo.
echo 🌐 访问地址:
echo 网关服务: http://localhost:8080
echo API文档: http://localhost:8081/doc.html
echo RabbitMQ管理: http://localhost:15672 (admin/admin123)
echo.
echo 💡 使用提示:
echo 1. 等待2-3分钟让所有服务完全启动
echo 2. 访问API文档查看接口说明
echo 3. 通过网关访问各个服务
echo 4. 查看RabbitMQ管理界面监控消息
goto end

:stop_services
echo.
echo ========================================
echo 停止所有服务...
echo ========================================
echo 正在停止Docker服务...
docker-compose down
echo 正在停止Java进程...
taskkill /f /im java.exe >nul 2>&1
echo 所有服务已停止！
goto end

:end
echo.
pause
