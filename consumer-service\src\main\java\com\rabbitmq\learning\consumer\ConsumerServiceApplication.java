package com.rabbitmq.learning.consumer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 消息消费者服务启动类
 * 
 * 该服务主要用于演示RabbitMQ的各种消息消费模式：
 * 1. 简单队列消费 (Simple Queue Consumer)
 * 2. 工作队列消费 (Work Queue Consumer)
 * 3. 发布订阅消费 (Publish/Subscribe Consumer)
 * 4. 路由消费 (Routing Consumer)
 * 5. 主题消费 (Topic Consumer)
 * 6. RPC消费 (RPC Consumer)
 * 7. 延时消息消费
 * 8. 死信队列消费
 * 9. 消息确认机制
 * 10. 消息重试机制
 * 11. 消息幂等性处理
 * 12. 消息顺序消费
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = "com.rabbitmq.learning")
@EnableDiscoveryClient
public class ConsumerServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(ConsumerServiceApplication.class, args);
        System.out.println("========================================");
        System.out.println("消息消费者服务启动成功！");
        System.out.println("API文档地址: http://localhost:8082/doc.html");
        System.out.println("健康检查: http://localhost:8082/actuator/health");
        System.out.println("========================================");
    }
}
