package com.rabbitmq.learning.producer.config;

import com.rabbitmq.learning.common.constant.RabbitMQConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ配置类
 * 配置交换机、队列、绑定关系以及RabbitTemplate
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class RabbitMQConfig {

    // ==================== RabbitTemplate配置 ====================

    /**
     * 配置RabbitTemplate
     * 设置消息转换器、确认回调、返回回调等
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        
        // 设置JSON消息转换器
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        
        // 设置发送确认回调 - 确认消息是否到达交换机
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("消息发送到交换机成功 - CorrelationData: {}", correlationData);
            } else {
                log.error("消息发送到交换机失败 - CorrelationData: {}, Cause: {}", correlationData, cause);
            }
        });
        
        // 设置返回回调 - 当消息无法路由到队列时触发
        rabbitTemplate.setReturnsCallback(returned -> {
            log.error("消息路由到队列失败 - Message: {}, ReplyCode: {}, ReplyText: {}, Exchange: {}, RoutingKey: {}",
                    returned.getMessage(), returned.getReplyCode(), returned.getReplyText(),
                    returned.getExchange(), returned.getRoutingKey());
        });
        
        // 开启强制模式 - 当消息无法路由时返回给生产者
        rabbitTemplate.setMandatory(true);
        
        return rabbitTemplate;
    }

    // ==================== 交换机配置 ====================

    /**
     * 订单直连交换机
     */
    @Bean
    public DirectExchange orderDirectExchange() {
        return ExchangeBuilder
                .directExchange(RabbitMQConstant.Exchange.ORDER_DIRECT)
                .durable(true)
                .build();
    }

    /**
     * 订单主题交换机
     */
    @Bean
    public TopicExchange orderTopicExchange() {
        return ExchangeBuilder
                .topicExchange(RabbitMQConstant.Exchange.ORDER_TOPIC)
                .durable(true)
                .build();
    }

    /**
     * 订单扇形交换机
     */
    @Bean
    public FanoutExchange orderFanoutExchange() {
        return ExchangeBuilder
                .fanoutExchange(RabbitMQConstant.Exchange.ORDER_FANOUT)
                .durable(true)
                .build();
    }

    /**
     * 库存直连交换机
     */
    @Bean
    public DirectExchange inventoryDirectExchange() {
        return ExchangeBuilder
                .directExchange(RabbitMQConstant.Exchange.INVENTORY_DIRECT)
                .durable(true)
                .build();
    }

    /**
     * 库存主题交换机
     */
    @Bean
    public TopicExchange inventoryTopicExchange() {
        return ExchangeBuilder
                .topicExchange(RabbitMQConstant.Exchange.INVENTORY_TOPIC)
                .durable(true)
                .build();
    }

    /**
     * 死信交换机
     */
    @Bean
    public DirectExchange deadLetterExchange() {
        return ExchangeBuilder
                .directExchange(RabbitMQConstant.Exchange.DEAD_LETTER)
                .durable(true)
                .build();
    }

    /**
     * 延时交换机 (需要安装rabbitmq-delayed-message-exchange插件)
     */
    @Bean
    public TopicExchange delayExchange() {
        return ExchangeBuilder
                .topicExchange(RabbitMQConstant.Exchange.DELAY)
                .durable(true)
                .build();
    }

    // ==================== 队列配置 ====================

    /**
     * 订单创建队列
     */
    @Bean
    public Queue orderCreateQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.ORDER_CREATE)
                .withArgument("x-dead-letter-exchange", RabbitMQConstant.Exchange.DEAD_LETTER)
                .withArgument("x-dead-letter-routing-key", RabbitMQConstant.RoutingKey.DEAD_LETTER)
                .withArgument("x-max-priority", RabbitMQConstant.Other.MAX_PRIORITY)
                .build();
    }

    /**
     * 订单支付队列
     */
    @Bean
    public Queue orderPaymentQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.ORDER_PAYMENT)
                .withArgument("x-dead-letter-exchange", RabbitMQConstant.Exchange.DEAD_LETTER)
                .withArgument("x-dead-letter-routing-key", RabbitMQConstant.RoutingKey.DEAD_LETTER)
                .build();
    }

    /**
     * 订单取消队列
     */
    @Bean
    public Queue orderCancelQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.ORDER_CANCEL)
                .build();
    }

    /**
     * 订单超时队列 - 设置TTL
     */
    @Bean
    public Queue orderTimeoutQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.ORDER_TIMEOUT)
                .withArgument("x-message-ttl", RabbitMQConstant.Other.ORDER_TIMEOUT_TTL)
                .withArgument("x-dead-letter-exchange", RabbitMQConstant.Exchange.DEAD_LETTER)
                .withArgument("x-dead-letter-routing-key", RabbitMQConstant.RoutingKey.DEAD_LETTER)
                .build();
    }

    /**
     * 库存锁定队列
     */
    @Bean
    public Queue inventoryLockQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.INVENTORY_LOCK)
                .build();
    }

    /**
     * 库存解锁队列
     */
    @Bean
    public Queue inventoryUnlockQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.INVENTORY_UNLOCK)
                .build();
    }

    /**
     * 死信队列
     */
    @Bean
    public Queue deadLetterQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.DEAD_LETTER)
                .build();
    }

    /**
     * 延时队列
     */
    @Bean
    public Queue delayQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.DELAY)
                .build();
    }

    /**
     * 工作队列 - 演示工作队列模式
     */
    @Bean
    public Queue workQueue() {
        return QueueBuilder
                .durable(RabbitMQConstant.Queue.WORK)
                .build();
    }

    // ==================== 绑定关系配置 ====================

    /**
     * 绑定订单创建队列到订单直连交换机
     */
    @Bean
    public Binding orderCreateBinding() {
        return BindingBuilder
                .bind(orderCreateQueue())
                .to(orderDirectExchange())
                .with(RabbitMQConstant.RoutingKey.ORDER_CREATE);
    }

    /**
     * 绑定订单支付队列到订单直连交换机
     */
    @Bean
    public Binding orderPaymentBinding() {
        return BindingBuilder
                .bind(orderPaymentQueue())
                .to(orderDirectExchange())
                .with(RabbitMQConstant.RoutingKey.ORDER_PAYMENT);
    }

    /**
     * 绑定订单取消队列到订单直连交换机
     */
    @Bean
    public Binding orderCancelBinding() {
        return BindingBuilder
                .bind(orderCancelQueue())
                .to(orderDirectExchange())
                .with(RabbitMQConstant.RoutingKey.ORDER_CANCEL);
    }

    /**
     * 绑定库存锁定队列到库存直连交换机
     */
    @Bean
    public Binding inventoryLockBinding() {
        return BindingBuilder
                .bind(inventoryLockQueue())
                .to(inventoryDirectExchange())
                .with(RabbitMQConstant.RoutingKey.INVENTORY_LOCK);
    }

    /**
     * 绑定库存解锁队列到库存直连交换机
     */
    @Bean
    public Binding inventoryUnlockBinding() {
        return BindingBuilder
                .bind(inventoryUnlockQueue())
                .to(inventoryDirectExchange())
                .with(RabbitMQConstant.RoutingKey.INVENTORY_UNLOCK);
    }

    /**
     * 绑定死信队列到死信交换机
     */
    @Bean
    public Binding deadLetterBinding() {
        return BindingBuilder
                .bind(deadLetterQueue())
                .to(deadLetterExchange())
                .with(RabbitMQConstant.RoutingKey.DEAD_LETTER);
    }

    /**
     * 绑定延时队列到延时交换机
     */
    @Bean
    public Binding delayBinding() {
        return BindingBuilder
                .bind(delayQueue())
                .to(delayExchange())
                .with(RabbitMQConstant.RoutingKey.DELAY)
                .noargs();
    }
}
