-- RabbitMQ学习项目数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建订单表
DROP TABLE IF EXISTS `t_order`;
CREATE TABLE `t_order` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单编号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `quantity` int NOT NULL COMMENT '购买数量',
  `price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `status` int NOT NULL DEFAULT '0' COMMENT '订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 5-已退款',
  `payment_method` int DEFAULT NULL COMMENT '支付方式 1-支付宝 2-微信 3-银行卡',
  `shipping_address` varchar(500) DEFAULT NULL COMMENT '收货地址',
  `receiver_name` varchar(100) DEFAULT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人电话',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建者ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新者ID',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '逻辑删除标志 0-未删除 1-已删除',
  `version` int NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 创建库存表
DROP TABLE IF EXISTS `t_inventory`;
CREATE TABLE `t_inventory` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `sku_code` varchar(100) NOT NULL COMMENT '商品SKU编码',
  `total_stock` int NOT NULL DEFAULT '0' COMMENT '总库存数量',
  `available_stock` int NOT NULL DEFAULT '0' COMMENT '可用库存数量',
  `locked_stock` int NOT NULL DEFAULT '0' COMMENT '锁定库存数量',
  `sold_stock` int NOT NULL DEFAULT '0' COMMENT '已售出数量',
  `warehouse_code` varchar(50) NOT NULL COMMENT '仓库编码',
  `warehouse_name` varchar(100) NOT NULL COMMENT '仓库名称',
  `status` int NOT NULL DEFAULT '0' COMMENT '库存状态 0-正常 1-缺货 2-停售',
  `safety_stock` int NOT NULL DEFAULT '0' COMMENT '安全库存阈值',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建者ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新者ID',
  `deleted` int NOT NULL DEFAULT '0' COMMENT '逻辑删除标志 0-未删除 1-已删除',
  `version` int NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_warehouse` (`product_id`, `warehouse_code`),
  KEY `idx_sku_code` (`sku_code`),
  KEY `idx_warehouse_code` (`warehouse_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存表';

-- 创建消息日志表
DROP TABLE IF EXISTS `t_message_log`;
CREATE TABLE `t_message_log` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `message_id` varchar(64) NOT NULL COMMENT '消息ID',
  `message_type` varchar(100) NOT NULL COMMENT '消息类型',
  `exchange_name` varchar(100) DEFAULT NULL COMMENT '交换机名称',
  `routing_key` varchar(100) DEFAULT NULL COMMENT '路由键',
  `queue_name` varchar(100) DEFAULT NULL COMMENT '队列名称',
  `message_body` text COMMENT '消息内容',
  `status` int NOT NULL DEFAULT '0' COMMENT '消息状态 0-发送中 1-发送成功 2-发送失败 3-消费成功 4-消费失败',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int NOT NULL DEFAULT '3' COMMENT '最大重试次数',
  `error_message` text COMMENT '错误信息',
  `source_service` varchar(100) DEFAULT NULL COMMENT '来源服务',
  `target_service` varchar(100) DEFAULT NULL COMMENT '目标服务',
  `business_id` varchar(100) DEFAULT NULL COMMENT '业务标识',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `consume_time` datetime DEFAULT NULL COMMENT '消费时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_message_type` (`message_type`),
  KEY `idx_status` (`status`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息日志表';

-- 插入测试数据
INSERT INTO `t_inventory` (`id`, `product_id`, `product_name`, `sku_code`, `total_stock`, `available_stock`, `locked_stock`, `sold_stock`, `warehouse_code`, `warehouse_name`, `status`, `safety_stock`, `remark`) VALUES
(1, 1001, 'iPhone 15 Pro', 'IP15P-256G-BLACK', 100, 95, 3, 2, 'WH001', '北京仓库', 0, 10, '苹果手机'),
(2, 1002, 'MacBook Pro', 'MBP-M3-16G-512G', 50, 48, 1, 1, 'WH001', '北京仓库', 0, 5, '苹果笔记本'),
(3, 1003, 'AirPods Pro', 'APP-3RD-WHITE', 200, 180, 10, 10, 'WH002', '上海仓库', 0, 20, '苹果耳机'),
(4, 1004, 'iPad Air', 'IPA-M2-256G-BLUE', 80, 75, 2, 3, 'WH002', '上海仓库', 0, 8, '苹果平板'),
(5, 1005, 'Apple Watch', 'AW-S9-45MM-GPS', 120, 110, 5, 5, 'WH003', '广州仓库', 0, 12, '苹果手表');

-- 插入测试订单数据
INSERT INTO `t_order` (`id`, `order_no`, `user_id`, `product_id`, `product_name`, `quantity`, `price`, `total_amount`, `status`, `payment_method`, `shipping_address`, `receiver_name`, `receiver_phone`, `remark`) VALUES
(1, 'ORD202401010001', 10001, 1001, 'iPhone 15 Pro', 1, 8999.00, 8999.00, 1, 1, '北京市朝阳区xxx街道xxx号', '张三', '13800138001', '测试订单1'),
(2, 'ORD202401010002', 10002, 1002, 'MacBook Pro', 1, 15999.00, 15999.00, 0, NULL, '上海市浦东新区xxx路xxx号', '李四', '13800138002', '测试订单2'),
(3, 'ORD202401010003', 10003, 1003, 'AirPods Pro', 2, 1899.00, 3798.00, 2, 2, '广州市天河区xxx大道xxx号', '王五', '13800138003', '测试订单3');

SET FOREIGN_KEY_CHECKS = 1;
