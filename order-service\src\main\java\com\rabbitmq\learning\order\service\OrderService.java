package com.rabbitmq.learning.order.service;

import com.rabbitmq.learning.common.entity.Order;
import com.rabbitmq.learning.common.message.OrderMessage;
import com.rabbitmq.learning.common.result.Result;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单服务接口
 * 定义订单相关的业务操作
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
public interface OrderService {

    /**
     * 创建订单
     * 
     * @param userId 用户ID
     * @param productId 商品ID
     * @param productName 商品名称
     * @param quantity 购买数量
     * @param price 单价
     * @return 创建结果
     */
    Result<Order> createOrder(Long userId, Long productId, String productName, Integer quantity, BigDecimal price);

    /**
     * 支付订单
     * 
     * @param orderNo 订单编号
     * @param paymentMethod 支付方式
     * @return 支付结果
     */
    Result<String> payOrder(String orderNo, Integer paymentMethod);

    /**
     * 取消订单
     * 
     * @param orderNo 订单编号
     * @param reason 取消原因
     * @return 取消结果
     */
    Result<String> cancelOrder(String orderNo, String reason);

    /**
     * 查询订单详情
     * 
     * @param orderNo 订单编号
     * @return 订单详情
     */
    Result<Order> getOrderDetail(String orderNo);

    /**
     * 查询用户订单列表
     * 
     * @param userId 用户ID
     * @param status 订单状态（可选）
     * @return 订单列表
     */
    Result<List<Order>> getUserOrders(Long userId, Integer status);

    /**
     * 处理订单创建消息
     * 
     * @param orderMessage 订单消息
     */
    void handleOrderCreateMessage(OrderMessage orderMessage);

    /**
     * 处理订单支付消息
     * 
     * @param orderMessage 订单消息
     */
    void handleOrderPaymentMessage(OrderMessage orderMessage);

    /**
     * 处理订单取消消息
     * 
     * @param orderMessage 订单消息
     */
    void handleOrderCancelMessage(OrderMessage orderMessage);

    /**
     * 处理订单超时消息
     * 
     * @param orderMessage 订单消息
     */
    void handleOrderTimeoutMessage(OrderMessage orderMessage);

    /**
     * 更新订单状态
     * 
     * @param orderNo 订单编号
     * @param status 新状态
     * @return 更新结果
     */
    Result<String> updateOrderStatus(String orderNo, Integer status);

    /**
     * 检查订单是否可以取消
     * 
     * @param orderNo 订单编号
     * @return 检查结果
     */
    boolean canCancelOrder(String orderNo);

    /**
     * 检查订单是否可以支付
     * 
     * @param orderNo 订单编号
     * @return 检查结果
     */
    boolean canPayOrder(String orderNo);

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    String generateOrderNo();
}
