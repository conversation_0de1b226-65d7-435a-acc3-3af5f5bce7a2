# 消费者服务配置文件
server:
  port: 8082
  servlet:
    context-path: /

spring:
  application:
    name: consumer-service
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    # 连接超时时间
    connection-timeout: 15000
    # 开启发送确认
    publisher-confirm-type: correlated
    # 开启发送失败退回
    publisher-returns: true
    # 消费者配置
    listener:
      simple:
        # 手动确认模式
        acknowledge-mode: manual
        # 消费者数量
        concurrency: 2
        max-concurrency: 5
        # 限制消费者每次只处理一条消息，处理完再继续下一条消息
        prefetch: 1
        # 启动消费者失败后的重试配置
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          max-interval: 10000
          multiplier: 1.0
        # 消费者标签策略
        consumer-tag-strategy: com.rabbitmq.learning.consumer.config.CustomConsumerTagStrategy
      # 直接模式配置
      direct:
        acknowledge-mode: manual
        consumers-per-queue: 2
        prefetch: 1

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 开启Mybatis二级缓存，默认为 true
    cache-enabled: false
    # 全局地开启或关闭配置文件中的所有映射器已经配置的任何缓存，默认为 true
    use-generated-keys: true
    default-executor-type: reuse
    # 设置超时时间，它决定驱动等待数据库响应的秒数
    default-statement-timeout: 25000
  global-config:
    db-config:
      # 全局默认主键类型
      id-type: assign_id
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  # 搜索指定包别名
  type-aliases-package: com.rabbitmq.learning.common.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml

# 日志配置
logging:
  level:
    com.rabbitmq.learning: debug
    org.springframework.amqp: debug
    org.springframework.rabbit: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: RabbitMQ学习项目 - 消费者服务API
    description: "演示RabbitMQ各种消息消费模式的API接口文档"
    version: 1.0.0
    concat: RabbitMQ Learning Team
    url: https://github.com/rabbitmq-learning
    email: <EMAIL>
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html
    terms-of-service-url: https://example.com/terms
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

# 自定义配置
rabbitmq:
  learning:
    # 消息处理配置
    message:
      # 最大重试次数
      max-retry-count: 3
      # 重试间隔（毫秒）
      retry-interval: 1000
      # 是否启用消息去重
      enable-deduplication: true
      # 消息去重缓存过期时间（秒）
      deduplication-cache-expire: 3600
    
    # 消费者配置
    consumer:
      # 是否启用消费者监控
      enable-monitoring: true
      # 消费者性能统计间隔（秒）
      performance-stat-interval: 60
      # 是否启用慢消费告警
      enable-slow-consumer-alert: true
      # 慢消费阈值（毫秒）
      slow-consumer-threshold: 5000
