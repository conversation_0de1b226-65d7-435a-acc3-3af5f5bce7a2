package com.rabbitmq.learning.common.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 基础实体类
 * 包含所有实体的公共字段：主键、创建时间、更新时间
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Data
public abstract class BaseEntity {

    /**
     * 主键ID - 使用雪花算法生成
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 创建时间 - 插入时自动填充
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间 - 插入和更新时自动填充
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 逻辑删除标志 0-未删除 1-已删除
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer deleted;

    /**
     * 版本号 - 用于乐观锁
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer version;
}
