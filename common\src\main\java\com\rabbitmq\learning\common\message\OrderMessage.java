package com.rabbitmq.learning.common.message;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订单相关消息
 * 用于订单业务场景中的消息传递
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderMessage extends BaseMessage {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 支付方式
     */
    private Integer paymentMethod;

    /**
     * 收货地址
     */
    private String shippingAddress;

    /**
     * 收货人信息
     */
    private String receiverInfo;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 默认构造函数
     */
    public OrderMessage() {
        super();
    }

    /**
     * 构造函数
     * 
     * @param orderNo 订单编号
     * @param userId 用户ID
     */
    public OrderMessage(String orderNo, Long userId) {
        super(orderNo, userId);
        this.orderNo = orderNo;
    }

    /**
     * 构造函数
     * 
     * @param orderNo 订单编号
     * @param userId 用户ID
     * @param operationType 操作类型
     */
    public OrderMessage(String orderNo, Long userId, String operationType) {
        this(orderNo, userId);
        this.operationType = operationType;
    }

    /**
     * 订单操作类型枚举
     */
    public static class OperationType {
        public static final String CREATE = "CREATE";           // 创建订单
        public static final String PAYMENT = "PAYMENT";         // 支付订单
        public static final String CANCEL = "CANCEL";           // 取消订单
        public static final String SHIP = "SHIP";               // 发货
        public static final String COMPLETE = "COMPLETE";       // 完成订单
        public static final String REFUND = "REFUND";           // 退款
        public static final String TIMEOUT = "TIMEOUT";         // 订单超时
    }
}
