package com.rabbitmq.learning.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存实体类
 * 用于演示RabbitMQ在库存管理场景中的应用
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_inventory")
public class Inventory extends BaseEntity {

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKU编码
     */
    private String skuCode;

    /**
     * 总库存数量
     */
    private Integer totalStock;

    /**
     * 可用库存数量
     */
    private Integer availableStock;

    /**
     * 锁定库存数量（已下单但未支付）
     */
    private Integer lockedStock;

    /**
     * 已售出数量
     */
    private Integer soldStock;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 库存状态
     * 0-正常 1-缺货 2-停售
     */
    private Integer status;

    /**
     * 安全库存阈值
     */
    private Integer safetyStock;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 库存状态枚举
     */
    public enum InventoryStatus {
        NORMAL(0, "正常"),
        OUT_OF_STOCK(1, "缺货"),
        DISCONTINUED(2, "停售");

        private final Integer code;
        private final String description;

        InventoryStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
