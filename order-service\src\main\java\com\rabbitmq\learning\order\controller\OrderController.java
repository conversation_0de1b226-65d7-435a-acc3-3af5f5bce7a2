package com.rabbitmq.learning.order.controller;

import com.rabbitmq.learning.common.entity.Order;
import com.rabbitmq.learning.common.result.Result;
import com.rabbitmq.learning.order.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单控制器
 * 提供订单相关的HTTP接口
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/order")
@Tag(name = "订单管理", description = "订单相关的API接口，演示RabbitMQ在实际业务场景中的应用")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 创建订单
     */
    @PostMapping("/create")
    @Operation(summary = "创建订单", description = "创建新订单，会自动锁定库存并设置超时检查")
    public Result<Order> createOrder(
            @Parameter(description = "用户ID", required = true)
            @RequestParam Long userId,
            @Parameter(description = "商品ID", required = true)
            @RequestParam Long productId,
            @Parameter(description = "商品名称", required = true)
            @RequestParam String productName,
            @Parameter(description = "购买数量", required = true)
            @RequestParam Integer quantity,
            @Parameter(description = "单价", required = true)
            @RequestParam BigDecimal price) {
        
        try {
            return orderService.createOrder(userId, productId, productName, quantity, price);
        } catch (Exception e) {
            log.error("创建订单失败: {}", e.getMessage(), e);
            return Result.error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 支付订单
     */
    @PostMapping("/pay")
    @Operation(summary = "支付订单", description = "支付指定订单，会触发库存扣减和后续业务流程")
    public Result<String> payOrder(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo,
            @Parameter(description = "支付方式 1-支付宝 2-微信 3-银行卡", required = true)
            @RequestParam Integer paymentMethod) {
        
        try {
            return orderService.payOrder(orderNo, paymentMethod);
        } catch (Exception e) {
            log.error("支付订单失败: {}", e.getMessage(), e);
            return Result.error("支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/cancel")
    @Operation(summary = "取消订单", description = "取消指定订单，会释放锁定的库存")
    public Result<String> cancelOrder(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo,
            @Parameter(description = "取消原因")
            @RequestParam(required = false) String reason) {
        
        try {
            return orderService.cancelOrder(orderNo, reason);
        } catch (Exception e) {
            log.error("取消订单失败: {}", e.getMessage(), e);
            return Result.error("取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询订单详情
     */
    @GetMapping("/detail")
    @Operation(summary = "查询订单详情", description = "根据订单编号查询订单详细信息")
    public Result<Order> getOrderDetail(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo) {
        
        try {
            return orderService.getOrderDetail(orderNo);
        } catch (Exception e) {
            log.error("查询订单详情失败: {}", e.getMessage(), e);
            return Result.error("查询订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户订单列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询用户订单列表", description = "查询指定用户的订单列表，可按状态筛选")
    public Result<List<Order>> getUserOrders(
            @Parameter(description = "用户ID", required = true)
            @RequestParam Long userId,
            @Parameter(description = "订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 5-已退款")
            @RequestParam(required = false) Integer status) {
        
        try {
            return orderService.getUserOrders(userId, status);
        } catch (Exception e) {
            log.error("查询用户订单列表失败: {}", e.getMessage(), e);
            return Result.error("查询用户订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     */
    @PostMapping("/update-status")
    @Operation(summary = "更新订单状态", description = "手动更新订单状态（管理员功能）")
    public Result<String> updateOrderStatus(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo,
            @Parameter(description = "新状态", required = true)
            @RequestParam Integer status) {
        
        try {
            return orderService.updateOrderStatus(orderNo, status);
        } catch (Exception e) {
            log.error("更新订单状态失败: {}", e.getMessage(), e);
            return Result.error("更新订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查订单是否可以取消
     */
    @GetMapping("/can-cancel")
    @Operation(summary = "检查订单是否可以取消", description = "检查指定订单是否可以取消")
    public Result<Boolean> canCancelOrder(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo) {
        
        try {
            boolean canCancel = orderService.canCancelOrder(orderNo);
            return Result.success(canCancel);
        } catch (Exception e) {
            log.error("检查订单取消状态失败: {}", e.getMessage(), e);
            return Result.error("检查订单取消状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查订单是否可以支付
     */
    @GetMapping("/can-pay")
    @Operation(summary = "检查订单是否可以支付", description = "检查指定订单是否可以支付")
    public Result<Boolean> canPayOrder(
            @Parameter(description = "订单编号", required = true)
            @RequestParam String orderNo) {
        
        try {
            boolean canPay = orderService.canPayOrder(orderNo);
            return Result.success(canPay);
        } catch (Exception e) {
            log.error("检查订单支付状态失败: {}", e.getMessage(), e);
            return Result.error("检查订单支付状态失败: " + e.getMessage());
        }
    }

    /**
     * 生成订单编号
     */
    @GetMapping("/generate-order-no")
    @Operation(summary = "生成订单编号", description = "生成新的订单编号（测试用）")
    public Result<String> generateOrderNo() {
        try {
            String orderNo = orderService.generateOrderNo();
            return Result.success(orderNo);
        } catch (Exception e) {
            log.error("生成订单编号失败: {}", e.getMessage(), e);
            return Result.error("生成订单编号失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查订单服务是否正常运行")
    public Result<String> health() {
        return Result.success("订单服务运行正常");
    }

    /**
     * 获取服务信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取服务信息", description = "获取订单服务的基本信息")
    public Result<Object> info() {
        return Result.success("订单服务 - 负责处理订单的创建、支付、取消等业务，演示RabbitMQ在实际业务场景中的应用");
    }
}
