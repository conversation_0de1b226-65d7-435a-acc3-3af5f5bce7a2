package com.rabbitmq.learning.consumer.listener;

import com.rabbitmq.client.Channel;
import com.rabbitmq.learning.common.constant.RabbitMQConstant;
import com.rabbitmq.learning.common.message.OrderMessage;
import com.rabbitmq.learning.common.util.RabbitMQUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 订单消息监听器
 * 处理订单相关的消息，演示实际业务场景中的消息消费
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@Component
public class OrderMessageListener {

    @Autowired
    private RabbitMQUtil rabbitMQUtil;

    /**
     * 处理订单创建消息
     * 当有新订单创建时触发
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.ORDER_CREATE)
    public void handleOrderCreateMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析订单消息
            OrderMessage orderMessage = rabbitMQUtil.parseMessage(message, OrderMessage.class);
            
            log.info("接收到订单创建消息 - OrderNo: {}, UserId: {}, ProductId: {}, Quantity: {}", 
                    orderMessage.getOrderNo(), orderMessage.getUserId(), 
                    orderMessage.getProductId(), orderMessage.getQuantity());
            
            // 处理订单创建业务逻辑
            processOrderCreate(orderMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("订单创建消息处理完成 - OrderNo: {}", orderMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("处理订单创建消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "订单创建");
        }
    }

    /**
     * 处理订单支付消息
     * 当订单支付成功时触发
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.ORDER_PAYMENT)
    public void handleOrderPaymentMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析订单消息
            OrderMessage orderMessage = rabbitMQUtil.parseMessage(message, OrderMessage.class);
            
            log.info("接收到订单支付消息 - OrderNo: {}, UserId: {}, TotalAmount: {}, PaymentMethod: {}", 
                    orderMessage.getOrderNo(), orderMessage.getUserId(), 
                    orderMessage.getTotalAmount(), orderMessage.getPaymentMethod());
            
            // 处理订单支付业务逻辑
            processOrderPayment(orderMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("订单支付消息处理完成 - OrderNo: {}", orderMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("处理订单支付消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "订单支付");
        }
    }

    /**
     * 处理订单取消消息
     * 当订单被取消时触发
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.ORDER_CANCEL)
    public void handleOrderCancelMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析订单消息
            OrderMessage orderMessage = rabbitMQUtil.parseMessage(message, OrderMessage.class);
            
            log.info("接收到订单取消消息 - OrderNo: {}, UserId: {}, Reason: {}", 
                    orderMessage.getOrderNo(), orderMessage.getUserId(), orderMessage.getRemark());
            
            // 处理订单取消业务逻辑
            processOrderCancel(orderMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("订单取消消息处理完成 - OrderNo: {}", orderMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("处理订单取消消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "订单取消");
        }
    }

    /**
     * 处理订单超时消息
     * 当订单超时需要检查状态时触发
     * 
     * @param message AMQP消息
     * @param channel 通道
     */
    @RabbitListener(queues = RabbitMQConstant.Queue.ORDER_TIMEOUT)
    public void handleOrderTimeoutMessage(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        
        try {
            // 解析订单消息
            OrderMessage orderMessage = rabbitMQUtil.parseMessage(message, OrderMessage.class);
            
            log.info("接收到订单超时检查消息 - OrderNo: {}, UserId: {}", 
                    orderMessage.getOrderNo(), orderMessage.getUserId());
            
            // 处理订单超时检查业务逻辑
            processOrderTimeout(orderMessage);
            
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            log.info("订单超时检查消息处理完成 - OrderNo: {}", orderMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("处理订单超时检查消息失败 - DeliveryTag: {}, Error: {}", deliveryTag, e.getMessage(), e);
            handleMessageError(message, channel, deliveryTag, "订单超时检查");
        }
    }

    // ==================== 业务处理方法 ====================

    /**
     * 处理订单创建业务逻辑
     * 
     * @param orderMessage 订单消息
     */
    private void processOrderCreate(OrderMessage orderMessage) {
        log.info("开始处理订单创建业务 - OrderNo: {}", orderMessage.getOrderNo());
        
        try {
            // 1. 验证订单信息
            validateOrderInfo(orderMessage);
            
            // 2. 检查商品库存
            checkProductInventory(orderMessage);
            
            // 3. 锁定库存
            lockInventory(orderMessage);
            
            // 4. 创建订单记录
            createOrderRecord(orderMessage);
            
            // 5. 发送库存锁定消息（如果需要）
            // sendInventoryLockMessage(orderMessage);
            
            log.info("订单创建业务处理完成 - OrderNo: {}", orderMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("订单创建业务处理失败 - OrderNo: {}, Error: {}", 
                    orderMessage.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理订单支付业务逻辑
     * 
     * @param orderMessage 订单消息
     */
    private void processOrderPayment(OrderMessage orderMessage) {
        log.info("开始处理订单支付业务 - OrderNo: {}", orderMessage.getOrderNo());
        
        try {
            // 1. 验证订单状态
            validateOrderStatus(orderMessage);
            
            // 2. 更新订单状态为已支付
            updateOrderStatusToPaid(orderMessage);
            
            // 3. 扣减库存
            deductInventory(orderMessage);
            
            // 4. 发送发货通知
            sendShippingNotification(orderMessage);
            
            // 5. 发送支付成功通知
            sendPaymentSuccessNotification(orderMessage);
            
            log.info("订单支付业务处理完成 - OrderNo: {}", orderMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("订单支付业务处理失败 - OrderNo: {}, Error: {}", 
                    orderMessage.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理订单取消业务逻辑
     * 
     * @param orderMessage 订单消息
     */
    private void processOrderCancel(OrderMessage orderMessage) {
        log.info("开始处理订单取消业务 - OrderNo: {}", orderMessage.getOrderNo());
        
        try {
            // 1. 验证订单状态
            validateOrderCancel(orderMessage);
            
            // 2. 更新订单状态为已取消
            updateOrderStatusToCancelled(orderMessage);
            
            // 3. 释放锁定的库存
            releaseLockedInventory(orderMessage);
            
            // 4. 处理退款（如果已支付）
            processRefund(orderMessage);
            
            // 5. 发送取消通知
            sendCancelNotification(orderMessage);
            
            log.info("订单取消业务处理完成 - OrderNo: {}", orderMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("订单取消业务处理失败 - OrderNo: {}, Error: {}", 
                    orderMessage.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理订单超时检查业务逻辑
     * 
     * @param orderMessage 订单消息
     */
    private void processOrderTimeout(OrderMessage orderMessage) {
        log.info("开始处理订单超时检查业务 - OrderNo: {}", orderMessage.getOrderNo());
        
        try {
            // 1. 查询订单当前状态
            String currentStatus = getCurrentOrderStatus(orderMessage.getOrderNo());
            
            // 2. 如果订单仍然是待支付状态，则自动取消
            if ("PENDING_PAYMENT".equals(currentStatus)) {
                log.info("订单超时未支付，自动取消 - OrderNo: {}", orderMessage.getOrderNo());
                
                // 设置取消原因
                orderMessage.setRemark("订单超时未支付，系统自动取消");
                
                // 执行订单取消逻辑
                processOrderCancel(orderMessage);
            } else {
                log.info("订单状态已变更，无需超时处理 - OrderNo: {}, Status: {}", 
                        orderMessage.getOrderNo(), currentStatus);
            }
            
            log.info("订单超时检查业务处理完成 - OrderNo: {}", orderMessage.getOrderNo());
            
        } catch (Exception e) {
            log.error("订单超时检查业务处理失败 - OrderNo: {}, Error: {}", 
                    orderMessage.getOrderNo(), e.getMessage(), e);
            throw e;
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 处理消息错误
     * 
     * @param message AMQP消息
     * @param channel 通道
     * @param deliveryTag 投递标签
     * @param messageType 消息类型
     */
    private void handleMessageError(Message message, Channel channel, long deliveryTag, String messageType) {
        try {
            if (rabbitMQUtil.isExceedMaxRetry(message)) {
                log.error("{}消息重试次数超限，拒绝消息 - DeliveryTag: {}", messageType, deliveryTag);
                channel.basicReject(deliveryTag, false);
            } else {
                rabbitMQUtil.incrementRetryCount(message);
                log.info("{}消息处理失败，重新入队 - DeliveryTag: {}, RetryCount: {}", 
                        messageType, deliveryTag, rabbitMQUtil.getRetryCount(message));
                channel.basicReject(deliveryTag, true);
            }
        } catch (IOException ioException) {
            log.error("消息确认失败 - DeliveryTag: {}, Error: {}", deliveryTag, ioException.getMessage());
        }
    }

    // ==================== 模拟业务方法 ====================
    // 以下方法为模拟业务逻辑，实际项目中应该调用相应的服务

    private void validateOrderInfo(OrderMessage orderMessage) {
        log.info("验证订单信息 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private void checkProductInventory(OrderMessage orderMessage) {
        log.info("检查商品库存 - ProductId: {}, Quantity: {}", 
                orderMessage.getProductId(), orderMessage.getQuantity());
    }

    private void lockInventory(OrderMessage orderMessage) {
        log.info("锁定库存 - ProductId: {}, Quantity: {}", 
                orderMessage.getProductId(), orderMessage.getQuantity());
    }

    private void createOrderRecord(OrderMessage orderMessage) {
        log.info("创建订单记录 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private void validateOrderStatus(OrderMessage orderMessage) {
        log.info("验证订单状态 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private void updateOrderStatusToPaid(OrderMessage orderMessage) {
        log.info("更新订单状态为已支付 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private void deductInventory(OrderMessage orderMessage) {
        log.info("扣减库存 - ProductId: {}, Quantity: {}", 
                orderMessage.getProductId(), orderMessage.getQuantity());
    }

    private void sendShippingNotification(OrderMessage orderMessage) {
        log.info("发送发货通知 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private void sendPaymentSuccessNotification(OrderMessage orderMessage) {
        log.info("发送支付成功通知 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private void validateOrderCancel(OrderMessage orderMessage) {
        log.info("验证订单取消条件 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private void updateOrderStatusToCancelled(OrderMessage orderMessage) {
        log.info("更新订单状态为已取消 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private void releaseLockedInventory(OrderMessage orderMessage) {
        log.info("释放锁定库存 - ProductId: {}, Quantity: {}", 
                orderMessage.getProductId(), orderMessage.getQuantity());
    }

    private void processRefund(OrderMessage orderMessage) {
        log.info("处理退款 - OrderNo: {}, Amount: {}", 
                orderMessage.getOrderNo(), orderMessage.getTotalAmount());
    }

    private void sendCancelNotification(OrderMessage orderMessage) {
        log.info("发送取消通知 - OrderNo: {}", orderMessage.getOrderNo());
    }

    private String getCurrentOrderStatus(String orderNo) {
        log.info("查询订单当前状态 - OrderNo: {}", orderNo);
        // 模拟返回订单状态
        return "PENDING_PAYMENT";
    }
}
