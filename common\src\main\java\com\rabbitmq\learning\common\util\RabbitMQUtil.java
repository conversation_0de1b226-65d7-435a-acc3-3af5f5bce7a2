package com.rabbitmq.learning.common.util;

import com.alibaba.fastjson2.JSON;
import com.rabbitmq.learning.common.constant.RabbitMQConstant;
import com.rabbitmq.learning.common.message.BaseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ工具类
 * 提供消息发送、接收的通用方法和工具函数
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@Component
public class RabbitMQUtil {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送消息到指定交换机和路由键
     * 
     * @param exchange 交换机名称
     * @param routingKey 路由键
     * @param message 消息对象
     */
    public void sendMessage(String exchange, String routingKey, BaseMessage message) {
        try {
            // 设置发送时间
            message.setSendTimeNow();
            
            // 构建消息属性
            MessageProperties properties = buildMessageProperties(message);
            
            // 转换为JSON字符串
            String messageBody = JSON.toJSONString(message);
            
            // 创建AMQP消息
            Message amqpMessage = new Message(messageBody.getBytes(), properties);
            
            // 发送消息
            rabbitTemplate.send(exchange, routingKey, amqpMessage);
            
            log.info("消息发送成功 - Exchange: {}, RoutingKey: {}, MessageId: {}, MessageType: {}", 
                    exchange, routingKey, message.getMessageId(), message.getMessageType());
                    
        } catch (Exception e) {
            log.error("消息发送失败 - Exchange: {}, RoutingKey: {}, MessageId: {}, Error: {}", 
                    exchange, routingKey, message.getMessageId(), e.getMessage(), e);
            throw new RuntimeException("消息发送失败", e);
        }
    }

    /**
     * 发送延时消息 (使用TTL实现)
     *
     * @param exchange 交换机名称
     * @param routingKey 路由键
     * @param message 消息对象
     * @param delayMillis 延时时间（毫秒）
     */
    public void sendDelayMessage(String exchange, String routingKey, BaseMessage message, long delayMillis) {
        try {
            // 设置发送时间
            message.setSendTimeNow();

            // 设置延时时间
            message.setExpiration(delayMillis);

            // 构建消息属性
            MessageProperties properties = buildMessageProperties(message);

            // 设置消息TTL（过期时间）
            properties.setExpiration(String.valueOf(delayMillis));

            // 转换为JSON字符串
            String messageBody = JSON.toJSONString(message);

            // 创建AMQP消息
            Message amqpMessage = new Message(messageBody.getBytes(), properties);

            // 发送消息
            rabbitTemplate.send(exchange, routingKey, amqpMessage);

            log.info("延时消息发送成功 - Exchange: {}, RoutingKey: {}, MessageId: {}, DelayMillis: {}",
                    exchange, routingKey, message.getMessageId(), delayMillis);

        } catch (Exception e) {
            log.error("延时消息发送失败 - Exchange: {}, RoutingKey: {}, MessageId: {}, Error: {}",
                    exchange, routingKey, message.getMessageId(), e.getMessage(), e);
            throw new RuntimeException("延时消息发送失败", e);
        }
    }

    /**
     * 构建消息属性
     * 
     * @param message 消息对象
     * @return 消息属性
     */
    private MessageProperties buildMessageProperties(BaseMessage message) {
        MessageProperties properties = new MessageProperties();
        
        // 设置消息ID
        properties.setMessageId(message.getMessageId());
        
        // 设置消息类型
        properties.setType(message.getMessageType());
        
        // 设置时间戳
        properties.setTimestamp(java.sql.Timestamp.valueOf(message.getCreateTime()));
        
        // 设置优先级
        if (message.getPriority() != null) {
            properties.setPriority(message.getPriority());
        }
        
        // 设置过期时间
        if (message.getExpiration() != null) {
            properties.setExpiration(message.getExpiration().toString());
        }
        
        // 设置自定义头信息
        Map<String, Object> headers = new HashMap<>();
        
        if (message.getRetryCount() != null) {
            headers.put(RabbitMQConstant.MessageProperty.RETRY_COUNT, message.getRetryCount());
        }
        
        if (message.getMaxRetryCount() != null) {
            headers.put(RabbitMQConstant.MessageProperty.MAX_RETRY_COUNT, message.getMaxRetryCount());
        }
        
        if (message.getCreateTime() != null) {
            headers.put(RabbitMQConstant.MessageProperty.CREATE_TIME, 
                    message.getCreateTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        }
        
        if (message.getSourceService() != null) {
            headers.put(RabbitMQConstant.MessageProperty.SOURCE_SERVICE, message.getSourceService());
        }
        
        if (message.getTargetService() != null) {
            headers.put(RabbitMQConstant.MessageProperty.TARGET_SERVICE, message.getTargetService());
        }
        
        if (message.getBusinessId() != null) {
            headers.put(RabbitMQConstant.MessageProperty.BUSINESS_ID, message.getBusinessId());
        }
        
        if (message.getUserId() != null) {
            headers.put(RabbitMQConstant.MessageProperty.USER_ID, message.getUserId());
        }
        
        properties.setHeaders(headers);
        
        return properties;
    }

    /**
     * 解析消息内容
     * 
     * @param message AMQP消息
     * @param clazz 目标类型
     * @return 解析后的对象
     */
    public <T> T parseMessage(Message message, Class<T> clazz) {
        try {
            String messageBody = new String(message.getBody());
            return JSON.parseObject(messageBody, clazz);
        } catch (Exception e) {
            log.error("消息解析失败 - MessageId: {}, Error: {}", 
                    message.getMessageProperties().getMessageId(), e.getMessage(), e);
            throw new RuntimeException("消息解析失败", e);
        }
    }

    /**
     * 获取消息重试次数
     * 
     * @param message AMQP消息
     * @return 重试次数
     */
    public Integer getRetryCount(Message message) {
        Map<String, Object> headers = message.getMessageProperties().getHeaders();
        Object retryCount = headers.get(RabbitMQConstant.MessageProperty.RETRY_COUNT);
        return retryCount != null ? (Integer) retryCount : 0;
    }

    /**
     * 增加消息重试次数
     * 
     * @param message AMQP消息
     */
    public void incrementRetryCount(Message message) {
        Map<String, Object> headers = message.getMessageProperties().getHeaders();
        Integer retryCount = getRetryCount(message);
        headers.put(RabbitMQConstant.MessageProperty.RETRY_COUNT, retryCount + 1);
    }

    /**
     * 判断消息是否超过最大重试次数
     * 
     * @param message AMQP消息
     * @return true-超过最大重试次数，false-未超过
     */
    public boolean isExceedMaxRetry(Message message) {
        Map<String, Object> headers = message.getMessageProperties().getHeaders();
        Integer retryCount = getRetryCount(message);
        Object maxRetryCount = headers.get(RabbitMQConstant.MessageProperty.MAX_RETRY_COUNT);
        int maxRetry = maxRetryCount != null ? (Integer) maxRetryCount : RabbitMQConstant.Other.DEFAULT_MAX_RETRY;
        return retryCount >= maxRetry;
    }

    /**
     * 记录消息处理日志
     * 
     * @param message AMQP消息
     * @param action 处理动作
     * @param success 是否成功
     * @param error 错误信息
     */
    public void logMessageProcess(Message message, String action, boolean success, String error) {
        MessageProperties properties = message.getMessageProperties();
        String messageId = properties.getMessageId();
        String messageType = properties.getType();
        
        if (success) {
            log.info("消息处理成功 - Action: {}, MessageId: {}, MessageType: {}", 
                    action, messageId, messageType);
        } else {
            log.error("消息处理失败 - Action: {}, MessageId: {}, MessageType: {}, Error: {}", 
                    action, messageId, messageType, error);
        }
    }
}
