package com.rabbitmq.learning.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rabbitmq.learning.common.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单信息
     */
    @Select("SELECT * FROM t_order WHERE order_no = #{orderNo} AND deleted = 0")
    Order selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @param status 订单状态（可选）
     * @return 订单列表
     */
    @Select("<script>" +
            "SELECT * FROM t_order WHERE user_id = #{userId} AND deleted = 0" +
            "<if test='status != null'> AND status = #{status}</if>" +
            " ORDER BY create_time DESC" +
            "</script>")
    List<Order> selectByUserId(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 更新订单状态
     * 
     * @param orderNo 订单编号
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @return 更新行数
     */
    @Update("UPDATE t_order SET status = #{newStatus}, update_time = NOW() " +
            "WHERE order_no = #{orderNo} AND status = #{oldStatus} AND deleted = 0")
    int updateOrderStatus(@Param("orderNo") String orderNo, 
                         @Param("oldStatus") Integer oldStatus, 
                         @Param("newStatus") Integer newStatus);

    /**
     * 查询超时未支付的订单
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时订单列表
     */
    @Select("SELECT * FROM t_order WHERE status = 0 AND deleted = 0 " +
            "AND create_time < DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)")
    List<Order> selectTimeoutOrders(@Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 批量更新订单状态为已取消
     * 
     * @param orderNos 订单编号列表
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE t_order SET status = 4, remark = '订单超时自动取消', update_time = NOW() " +
            "WHERE order_no IN " +
            "<foreach collection='orderNos' item='orderNo' open='(' separator=',' close=')'>" +
            "#{orderNo}" +
            "</foreach>" +
            " AND status = 0 AND deleted = 0" +
            "</script>")
    int batchCancelTimeoutOrders(@Param("orderNos") List<String> orderNos);

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @param status 订单状态（可选）
     * @return 订单数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_order WHERE user_id = #{userId} AND deleted = 0" +
            "<if test='status != null'> AND status = #{status}</if>" +
            "</script>")
    Long countUserOrders(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 查询指定时间范围内的订单
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 订单状态（可选）
     * @return 订单列表
     */
    @Select("<script>" +
            "SELECT * FROM t_order WHERE deleted = 0" +
            "<if test='startTime != null'> AND create_time >= #{startTime}</if>" +
            "<if test='endTime != null'> AND create_time <= #{endTime}</if>" +
            "<if test='status != null'> AND status = #{status}</if>" +
            " ORDER BY create_time DESC" +
            "</script>")
    List<Order> selectOrdersByTimeRange(@Param("startTime") String startTime, 
                                       @Param("endTime") String endTime, 
                                       @Param("status") Integer status);
}
