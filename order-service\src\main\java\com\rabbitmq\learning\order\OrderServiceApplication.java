package com.rabbitmq.learning.order;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 订单服务启动类
 * 
 * 该服务主要功能：
 * 1. 订单管理 - 创建、查询、更新、取消订单
 * 2. 订单状态流转 - 待支付 -> 已支付 -> 已发货 -> 已完成
 * 3. 消息驱动 - 通过RabbitMQ处理订单相关的异步业务
 * 4. 分布式事务 - 与库存服务协调，保证数据一致性
 * 5. 订单超时处理 - 自动取消超时未支付的订单
 * 6. 支付回调处理 - 处理支付成功/失败的回调
 * 7. 库存预扣和释放 - 下单时预扣库存，取消时释放库存
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = "com.rabbitmq.learning")
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan("com.rabbitmq.learning.order.mapper")
public class OrderServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderServiceApplication.class, args);
        System.out.println("========================================");
        System.out.println("订单服务启动成功！");
        System.out.println("API文档地址: http://localhost:8083/doc.html");
        System.out.println("健康检查: http://localhost:8083/actuator/health");
        System.out.println("========================================");
    }
}
