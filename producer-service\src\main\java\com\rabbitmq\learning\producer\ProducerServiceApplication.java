package com.rabbitmq.learning.producer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 消息生产者服务启动类
 * 
 * 该服务主要用于演示RabbitMQ的各种消息发送模式：
 * 1. 简单队列模式 (Simple Queue)
 * 2. 工作队列模式 (Work Queue)
 * 3. 发布订阅模式 (Publish/Subscribe)
 * 4. 路由模式 (Routing)
 * 5. 主题模式 (Topic)
 * 6. RPC模式 (RPC)
 * 7. 延时消息
 * 8. 死信队列
 * 9. 消息确认机制
 * 10. 事务消息
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = "com.rabbitmq.learning")
@EnableDiscoveryClient
public class ProducerServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProducerServiceApplication.class, args);
        System.out.println("========================================");
        System.out.println("消息生产者服务启动成功！");
        System.out.println("API文档地址: http://localhost:8081/doc.html");
        System.out.println("健康检查: http://localhost:8081/actuator/health");
        System.out.println("========================================");
    }
}
