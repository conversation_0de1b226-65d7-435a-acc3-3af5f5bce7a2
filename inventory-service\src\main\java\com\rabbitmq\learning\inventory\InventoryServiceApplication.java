package com.rabbitmq.learning.inventory;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 库存服务启动类
 * 
 * 该服务主要功能：
 * 1. 库存管理 - 库存查询、锁定、解锁、扣减
 * 2. 分布式事务 - 与订单服务协调，保证数据一致性
 * 3. 消息补偿机制 - 处理异常情况下的库存回滚
 * 4. 库存预警 - 当库存低于安全阈值时发送预警
 * 5. 库存同步 - 同步库存数据到缓存和搜索引擎
 * 6. 库存变更日志 - 记录所有库存变更操作
 * 7. 并发控制 - 使用分布式锁保证库存操作的原子性
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = "com.rabbitmq.learning")
@EnableDiscoveryClient
@EnableTransactionManagement
@MapperScan("com.rabbitmq.learning.inventory.mapper")
public class InventoryServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(InventoryServiceApplication.class, args);
        System.out.println("========================================");
        System.out.println("库存服务启动成功！");
        System.out.println("API文档地址: http://localhost:8084/doc.html");
        System.out.println("健康检查: http://localhost:8084/actuator/health");
        System.out.println("========================================");
    }
}
