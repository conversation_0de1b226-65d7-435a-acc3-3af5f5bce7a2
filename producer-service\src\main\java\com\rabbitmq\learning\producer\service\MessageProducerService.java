package com.rabbitmq.learning.producer.service;

import com.rabbitmq.learning.common.constant.RabbitMQConstant;
import com.rabbitmq.learning.common.message.BaseMessage;
import com.rabbitmq.learning.common.message.InventoryMessage;
import com.rabbitmq.learning.common.message.OrderMessage;
import com.rabbitmq.learning.common.util.RabbitMQUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 消息生产者服务
 * 提供各种消息发送功能，演示不同的消息模式
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@Service
public class MessageProducerService {

    @Autowired
    private RabbitMQUtil rabbitMQUtil;

    // ==================== 简单队列模式 ====================

    /**
     * 发送简单消息到工作队列
     * 演示最基本的消息发送模式
     * 
     * @param message 消息内容
     */
    public void sendSimpleMessage(String message) {
        BaseMessage baseMessage = new BaseMessage();
        baseMessage.setSourceService("producer-service");
        baseMessage.setTargetService("consumer-service");
        baseMessage.setExtendInfo(message);
        
        // 直接发送到队列，不使用交换机
        rabbitMQUtil.sendMessage("", RabbitMQConstant.Queue.WORK, baseMessage);
        
        log.info("发送简单消息成功: {}", message);
    }

    // ==================== 工作队列模式 ====================

    /**
     * 发送工作队列消息
     * 演示多个消费者竞争消费同一队列的消息
     * 
     * @param taskContent 任务内容
     * @param taskId 任务ID
     */
    public void sendWorkQueueMessage(String taskContent, String taskId) {
        BaseMessage message = new BaseMessage(taskId);
        message.setSourceService("producer-service");
        message.setTargetService("consumer-service");
        message.setExtendInfo(taskContent);
        
        rabbitMQUtil.sendMessage("", RabbitMQConstant.Queue.WORK, message);
        
        log.info("发送工作队列消息成功 - TaskId: {}, Content: {}", taskId, taskContent);
    }

    // ==================== 发布订阅模式 ====================

    /**
     * 发送发布订阅消息
     * 演示一条消息被多个队列接收
     * 
     * @param message 消息内容
     */
    public void sendPublishSubscribeMessage(String message) {
        BaseMessage baseMessage = new BaseMessage();
        baseMessage.setSourceService("producer-service");
        baseMessage.setExtendInfo(message);
        
        // 发送到扇形交换机，所有绑定的队列都会收到消息
        rabbitMQUtil.sendMessage(RabbitMQConstant.Exchange.ORDER_FANOUT, "", baseMessage);
        
        log.info("发送发布订阅消息成功: {}", message);
    }

    // ==================== 路由模式 ====================

    /**
     * 发送路由消息
     * 演示根据路由键精确匹配队列
     * 
     * @param routingKey 路由键
     * @param message 消息内容
     */
    public void sendRoutingMessage(String routingKey, String message) {
        BaseMessage baseMessage = new BaseMessage();
        baseMessage.setSourceService("producer-service");
        baseMessage.setExtendInfo(message);
        
        rabbitMQUtil.sendMessage(RabbitMQConstant.Exchange.ORDER_DIRECT, routingKey, baseMessage);
        
        log.info("发送路由消息成功 - RoutingKey: {}, Message: {}", routingKey, message);
    }

    // ==================== 主题模式 ====================

    /**
     * 发送主题消息
     * 演示根据主题模式匹配队列
     * 
     * @param topic 主题
     * @param message 消息内容
     */
    public void sendTopicMessage(String topic, String message) {
        BaseMessage baseMessage = new BaseMessage();
        baseMessage.setSourceService("producer-service");
        baseMessage.setExtendInfo(message);
        
        rabbitMQUtil.sendMessage(RabbitMQConstant.Exchange.ORDER_TOPIC, topic, baseMessage);
        
        log.info("发送主题消息成功 - Topic: {}, Message: {}", topic, message);
    }

    // ==================== 订单业务消息 ====================

    /**
     * 发送订单创建消息
     * 
     * @param orderMessage 订单消息
     */
    public void sendOrderCreateMessage(OrderMessage orderMessage) {
        orderMessage.setSourceService("producer-service");
        orderMessage.setTargetService("order-service");
        orderMessage.setOperationType(OrderMessage.OperationType.CREATE);
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.ORDER_DIRECT,
                RabbitMQConstant.RoutingKey.ORDER_CREATE,
                orderMessage
        );
        
        log.info("发送订单创建消息成功 - OrderNo: {}", orderMessage.getOrderNo());
    }

    /**
     * 发送订单支付消息
     * 
     * @param orderMessage 订单消息
     */
    public void sendOrderPaymentMessage(OrderMessage orderMessage) {
        orderMessage.setSourceService("producer-service");
        orderMessage.setTargetService("order-service");
        orderMessage.setOperationType(OrderMessage.OperationType.PAYMENT);
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.ORDER_DIRECT,
                RabbitMQConstant.RoutingKey.ORDER_PAYMENT,
                orderMessage
        );
        
        log.info("发送订单支付消息成功 - OrderNo: {}", orderMessage.getOrderNo());
    }

    /**
     * 发送订单取消消息
     * 
     * @param orderMessage 订单消息
     */
    public void sendOrderCancelMessage(OrderMessage orderMessage) {
        orderMessage.setSourceService("producer-service");
        orderMessage.setTargetService("order-service");
        orderMessage.setOperationType(OrderMessage.OperationType.CANCEL);
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.ORDER_DIRECT,
                RabbitMQConstant.RoutingKey.ORDER_CANCEL,
                orderMessage
        );
        
        log.info("发送订单取消消息成功 - OrderNo: {}", orderMessage.getOrderNo());
    }

    // ==================== 库存业务消息 ====================

    /**
     * 发送库存锁定消息
     * 
     * @param inventoryMessage 库存消息
     */
    public void sendInventoryLockMessage(InventoryMessage inventoryMessage) {
        inventoryMessage.setSourceService("producer-service");
        inventoryMessage.setTargetService("inventory-service");
        inventoryMessage.setOperationType(InventoryMessage.OperationType.LOCK);
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.INVENTORY_DIRECT,
                RabbitMQConstant.RoutingKey.INVENTORY_LOCK,
                inventoryMessage
        );
        
        log.info("发送库存锁定消息成功 - ProductId: {}, Quantity: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getQuantity());
    }

    /**
     * 发送库存解锁消息
     * 
     * @param inventoryMessage 库存消息
     */
    public void sendInventoryUnlockMessage(InventoryMessage inventoryMessage) {
        inventoryMessage.setSourceService("producer-service");
        inventoryMessage.setTargetService("inventory-service");
        inventoryMessage.setOperationType(InventoryMessage.OperationType.UNLOCK);
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.INVENTORY_DIRECT,
                RabbitMQConstant.RoutingKey.INVENTORY_UNLOCK,
                inventoryMessage
        );
        
        log.info("发送库存解锁消息成功 - ProductId: {}, Quantity: {}", 
                inventoryMessage.getProductId(), inventoryMessage.getQuantity());
    }

    // ==================== 延时消息 ====================

    /**
     * 发送延时消息
     * 演示延时消息的使用场景，如订单超时取消
     * 
     * @param message 消息内容
     * @param delayMillis 延时时间（毫秒）
     */
    public void sendDelayMessage(BaseMessage message, long delayMillis) {
        message.setSourceService("producer-service");
        
        rabbitMQUtil.sendDelayMessage(
                RabbitMQConstant.Exchange.DELAY,
                RabbitMQConstant.RoutingKey.DELAY,
                message,
                delayMillis
        );
        
        log.info("发送延时消息成功 - MessageId: {}, DelayMillis: {}", 
                message.getMessageId(), delayMillis);
    }

    /**
     * 发送订单超时检查消息
     * 
     * @param orderNo 订单编号
     * @param delayMinutes 延时分钟数
     */
    public void sendOrderTimeoutCheckMessage(String orderNo, int delayMinutes) {
        OrderMessage orderMessage = new OrderMessage(orderNo, null);
        orderMessage.setOperationType(OrderMessage.OperationType.TIMEOUT);
        orderMessage.setSourceService("producer-service");
        orderMessage.setTargetService("order-service");
        
        long delayMillis = delayMinutes * 60 * 1000L;
        sendDelayMessage(orderMessage, delayMillis);
        
        log.info("发送订单超时检查消息成功 - OrderNo: {}, DelayMinutes: {}", orderNo, delayMinutes);
    }

    // ==================== 优先级消息 ====================

    /**
     * 发送优先级消息
     * 演示消息优先级的使用
     * 
     * @param message 消息内容
     * @param priority 优先级 (0-10，数字越大优先级越高)
     */
    public void sendPriorityMessage(String message, int priority) {
        BaseMessage baseMessage = new BaseMessage();
        baseMessage.setSourceService("producer-service");
        baseMessage.setExtendInfo(message);
        baseMessage.setPriority(priority);
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.ORDER_DIRECT,
                RabbitMQConstant.RoutingKey.ORDER_CREATE,
                baseMessage
        );
        
        log.info("发送优先级消息成功 - Message: {}, Priority: {}", message, priority);
    }
}
