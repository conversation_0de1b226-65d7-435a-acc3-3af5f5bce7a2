package com.rabbitmq.learning.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rabbitmq.learning.common.constant.RabbitMQConstant;
import com.rabbitmq.learning.common.entity.Order;
import com.rabbitmq.learning.common.message.InventoryMessage;
import com.rabbitmq.learning.common.message.OrderMessage;
import com.rabbitmq.learning.common.result.Result;
import com.rabbitmq.learning.common.result.ResultCode;
import com.rabbitmq.learning.common.util.RabbitMQUtil;
import com.rabbitmq.learning.order.mapper.OrderMapper;
import com.rabbitmq.learning.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 订单服务实现类
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private RabbitMQUtil rabbitMQUtil;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Order> createOrder(Long userId, Long productId, String productName, Integer quantity, BigDecimal price) {
        // 使用分布式锁防止重复下单
        String lockKey = "order:create:" + userId + ":" + productId;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // 尝试获取锁，最多等待10秒，锁定30秒
            if (lock.tryLock(10, 30, TimeUnit.SECONDS)) {
                try {
                    log.info("开始创建订单 - UserId: {}, ProductId: {}, Quantity: {}", userId, productId, quantity);
                    
                    // 1. 生成订单编号
                    String orderNo = generateOrderNo();
                    
                    // 2. 计算总金额
                    BigDecimal totalAmount = price.multiply(BigDecimal.valueOf(quantity));
                    
                    // 3. 创建订单对象
                    Order order = new Order();
                    order.setOrderNo(orderNo);
                    order.setUserId(userId);
                    order.setProductId(productId);
                    order.setProductName(productName);
                    order.setQuantity(quantity);
                    order.setPrice(price);
                    order.setTotalAmount(totalAmount);
                    order.setStatus(Order.OrderStatus.PENDING_PAYMENT.getCode());
                    order.setCreateTime(LocalDateTime.now());
                    order.setUpdateTime(LocalDateTime.now());
                    
                    // 4. 保存订单到数据库
                    int result = orderMapper.insert(order);
                    if (result <= 0) {
                        return Result.error(ResultCode.DATABASE_ERROR);
                    }
                    
                    // 5. 发送库存锁定消息
                    sendInventoryLockMessage(order);
                    
                    // 6. 发送订单超时检查消息（15分钟后检查）
                    sendOrderTimeoutCheckMessage(orderNo, 15);
                    
                    log.info("订单创建成功 - OrderNo: {}", orderNo);
                    return Result.success("订单创建成功", order);
                    
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("获取分布式锁失败，订单创建被拒绝 - UserId: {}, ProductId: {}", userId, productId);
                return Result.error("系统繁忙，请稍后重试");
            }
        } catch (InterruptedException e) {
            log.error("获取分布式锁被中断 - UserId: {}, ProductId: {}", userId, productId, e);
            Thread.currentThread().interrupt();
            return Result.error("系统繁忙，请稍后重试");
        } catch (Exception e) {
            log.error("创建订单失败 - UserId: {}, ProductId: {}, Error: {}", userId, productId, e.getMessage(), e);
            return Result.error("订单创建失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> payOrder(String orderNo, Integer paymentMethod) {
        try {
            log.info("开始处理订单支付 - OrderNo: {}, PaymentMethod: {}", orderNo, paymentMethod);
            
            // 1. 查询订单
            Order order = getOrderByOrderNo(orderNo);
            if (order == null) {
                return Result.error(ResultCode.ORDER_NOT_FOUND);
            }
            
            // 2. 检查订单状态
            if (!canPayOrder(orderNo)) {
                return Result.error(ResultCode.ORDER_STATUS_ERROR);
            }
            
            // 3. 更新订单状态为已支付
            LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Order::getOrderNo, orderNo)
                    .eq(Order::getStatus, Order.OrderStatus.PENDING_PAYMENT.getCode())
                    .set(Order::getStatus, Order.OrderStatus.PAID.getCode())
                    .set(Order::getPaymentMethod, paymentMethod)
                    .set(Order::getUpdateTime, LocalDateTime.now());
            
            int result = orderMapper.update(null, updateWrapper);
            if (result <= 0) {
                return Result.error("订单支付失败，订单状态已变更");
            }
            
            // 4. 发送订单支付成功消息
            sendOrderPaymentMessage(order, paymentMethod);
            
            // 5. 发送库存扣减消息
            sendInventoryDeductMessage(order);
            
            log.info("订单支付成功 - OrderNo: {}", orderNo);
            return Result.success("订单支付成功");
            
        } catch (Exception e) {
            log.error("订单支付失败 - OrderNo: {}, Error: {}", orderNo, e.getMessage(), e);
            return Result.error("订单支付失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> cancelOrder(String orderNo, String reason) {
        try {
            log.info("开始取消订单 - OrderNo: {}, Reason: {}", orderNo, reason);
            
            // 1. 查询订单
            Order order = getOrderByOrderNo(orderNo);
            if (order == null) {
                return Result.error(ResultCode.ORDER_NOT_FOUND);
            }
            
            // 2. 检查订单是否可以取消
            if (!canCancelOrder(orderNo)) {
                return Result.error(ResultCode.ORDER_CANNOT_CANCEL);
            }
            
            // 3. 更新订单状态为已取消
            LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Order::getOrderNo, orderNo)
                    .set(Order::getStatus, Order.OrderStatus.CANCELLED.getCode())
                    .set(Order::getRemark, reason)
                    .set(Order::getUpdateTime, LocalDateTime.now());
            
            int result = orderMapper.update(null, updateWrapper);
            if (result <= 0) {
                return Result.error("订单取消失败");
            }
            
            // 4. 发送订单取消消息
            sendOrderCancelMessage(order, reason);
            
            // 5. 发送库存解锁消息
            sendInventoryUnlockMessage(order);
            
            log.info("订单取消成功 - OrderNo: {}", orderNo);
            return Result.success("订单取消成功");
            
        } catch (Exception e) {
            log.error("订单取消失败 - OrderNo: {}, Error: {}", orderNo, e.getMessage(), e);
            return Result.error("订单取消失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Order> getOrderDetail(String orderNo) {
        try {
            Order order = getOrderByOrderNo(orderNo);
            if (order == null) {
                return Result.error(ResultCode.ORDER_NOT_FOUND);
            }
            return Result.success(order);
        } catch (Exception e) {
            log.error("查询订单详情失败 - OrderNo: {}, Error: {}", orderNo, e.getMessage(), e);
            return Result.error("查询订单详情失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Order>> getUserOrders(Long userId, Integer status) {
        try {
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Order::getUserId, userId);
            if (status != null) {
                queryWrapper.eq(Order::getStatus, status);
            }
            queryWrapper.orderByDesc(Order::getCreateTime);
            
            List<Order> orders = orderMapper.selectList(queryWrapper);
            return Result.success(orders);
        } catch (Exception e) {
            log.error("查询用户订单列表失败 - UserId: {}, Error: {}", userId, e.getMessage(), e);
            return Result.error("查询订单列表失败：" + e.getMessage());
        }
    }

    @Override
    public void handleOrderCreateMessage(OrderMessage orderMessage) {
        log.info("处理订单创建消息 - OrderNo: {}", orderMessage.getOrderNo());
        // 这里可以添加订单创建后的额外处理逻辑
        // 例如：发送邮件通知、更新用户积分等
    }

    @Override
    public void handleOrderPaymentMessage(OrderMessage orderMessage) {
        log.info("处理订单支付消息 - OrderNo: {}", orderMessage.getOrderNo());
        // 这里可以添加订单支付后的额外处理逻辑
        // 例如：发送支付成功通知、更新会员等级等
    }

    @Override
    public void handleOrderCancelMessage(OrderMessage orderMessage) {
        log.info("处理订单取消消息 - OrderNo: {}", orderMessage.getOrderNo());
        // 这里可以添加订单取消后的额外处理逻辑
        // 例如：发送取消通知、处理退款等
    }

    @Override
    public void handleOrderTimeoutMessage(OrderMessage orderMessage) {
        log.info("处理订单超时消息 - OrderNo: {}", orderMessage.getOrderNo());
        
        // 查询订单当前状态
        Order order = getOrderByOrderNo(orderMessage.getOrderNo());
        if (order != null && Order.OrderStatus.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            // 如果订单仍然是待支付状态，则自动取消
            cancelOrder(orderMessage.getOrderNo(), "订单超时未支付，系统自动取消");
        }
    }

    @Override
    public Result<String> updateOrderStatus(String orderNo, Integer status) {
        try {
            LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Order::getOrderNo, orderNo)
                    .set(Order::getStatus, status)
                    .set(Order::getUpdateTime, LocalDateTime.now());
            
            int result = orderMapper.update(null, updateWrapper);
            if (result > 0) {
                return Result.success("订单状态更新成功");
            } else {
                return Result.error("订单状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新订单状态失败 - OrderNo: {}, Status: {}, Error: {}", orderNo, status, e.getMessage(), e);
            return Result.error("更新订单状态失败：" + e.getMessage());
        }
    }

    @Override
    public boolean canCancelOrder(String orderNo) {
        Order order = getOrderByOrderNo(orderNo);
        if (order == null) {
            return false;
        }
        
        // 只有待支付和已支付状态的订单可以取消
        Integer status = order.getStatus();
        return Order.OrderStatus.PENDING_PAYMENT.getCode().equals(status) ||
               Order.OrderStatus.PAID.getCode().equals(status);
    }

    @Override
    public boolean canPayOrder(String orderNo) {
        Order order = getOrderByOrderNo(orderNo);
        if (order == null) {
            return false;
        }
        
        // 只有待支付状态的订单可以支付
        return Order.OrderStatus.PENDING_PAYMENT.getCode().equals(order.getStatus());
    }

    @Override
    public String generateOrderNo() {
        // 生成订单编号：ORD + 年月日 + 时分秒 + 3位随机数
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = (int) (Math.random() * 1000);
        return String.format("ORD%s%03d", timestamp, random);
    }

    // ==================== 私有方法 ====================

    /**
     * 根据订单编号查询订单
     */
    private Order getOrderByOrderNo(String orderNo) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderNo, orderNo);
        return orderMapper.selectOne(queryWrapper);
    }

    /**
     * 发送库存锁定消息
     */
    private void sendInventoryLockMessage(Order order) {
        InventoryMessage inventoryMessage = new InventoryMessage(
                order.getProductId(), 
                order.getQuantity(), 
                InventoryMessage.OperationType.LOCK, 
                order.getOrderNo()
        );
        inventoryMessage.setSourceService("order-service");
        inventoryMessage.setTargetService("inventory-service");
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.INVENTORY_DIRECT,
                RabbitMQConstant.RoutingKey.INVENTORY_LOCK,
                inventoryMessage
        );
    }

    /**
     * 发送库存解锁消息
     */
    private void sendInventoryUnlockMessage(Order order) {
        InventoryMessage inventoryMessage = new InventoryMessage(
                order.getProductId(), 
                order.getQuantity(), 
                InventoryMessage.OperationType.UNLOCK, 
                order.getOrderNo()
        );
        inventoryMessage.setSourceService("order-service");
        inventoryMessage.setTargetService("inventory-service");
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.INVENTORY_DIRECT,
                RabbitMQConstant.RoutingKey.INVENTORY_UNLOCK,
                inventoryMessage
        );
    }

    /**
     * 发送库存扣减消息
     */
    private void sendInventoryDeductMessage(Order order) {
        InventoryMessage inventoryMessage = new InventoryMessage(
                order.getProductId(), 
                order.getQuantity(), 
                InventoryMessage.OperationType.DEDUCT, 
                order.getOrderNo()
        );
        inventoryMessage.setSourceService("order-service");
        inventoryMessage.setTargetService("inventory-service");
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.INVENTORY_DIRECT,
                RabbitMQConstant.RoutingKey.INVENTORY_DEDUCT,
                inventoryMessage
        );
    }

    /**
     * 发送订单支付消息
     */
    private void sendOrderPaymentMessage(Order order, Integer paymentMethod) {
        OrderMessage orderMessage = new OrderMessage(order.getOrderNo(), order.getUserId());
        orderMessage.setProductId(order.getProductId());
        orderMessage.setProductName(order.getProductName());
        orderMessage.setQuantity(order.getQuantity());
        orderMessage.setPrice(order.getPrice());
        orderMessage.setTotalAmount(order.getTotalAmount());
        orderMessage.setPaymentMethod(paymentMethod);
        orderMessage.setOperationType(OrderMessage.OperationType.PAYMENT);
        orderMessage.setSourceService("order-service");
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.ORDER_DIRECT,
                RabbitMQConstant.RoutingKey.ORDER_PAYMENT,
                orderMessage
        );
    }

    /**
     * 发送订单取消消息
     */
    private void sendOrderCancelMessage(Order order, String reason) {
        OrderMessage orderMessage = new OrderMessage(order.getOrderNo(), order.getUserId());
        orderMessage.setProductId(order.getProductId());
        orderMessage.setProductName(order.getProductName());
        orderMessage.setQuantity(order.getQuantity());
        orderMessage.setRemark(reason);
        orderMessage.setOperationType(OrderMessage.OperationType.CANCEL);
        orderMessage.setSourceService("order-service");
        
        rabbitMQUtil.sendMessage(
                RabbitMQConstant.Exchange.ORDER_DIRECT,
                RabbitMQConstant.RoutingKey.ORDER_CANCEL,
                orderMessage
        );
    }

    /**
     * 发送订单超时检查消息
     */
    private void sendOrderTimeoutCheckMessage(String orderNo, int delayMinutes) {
        OrderMessage orderMessage = new OrderMessage(orderNo, null);
        orderMessage.setOperationType(OrderMessage.OperationType.TIMEOUT);
        orderMessage.setSourceService("order-service");
        
        long delayMillis = delayMinutes * 60 * 1000L;
        rabbitMQUtil.sendDelayMessage(
                RabbitMQConstant.Exchange.DELAY,
                RabbitMQConstant.RoutingKey.DELAY,
                orderMessage,
                delayMillis
        );
    }
}
