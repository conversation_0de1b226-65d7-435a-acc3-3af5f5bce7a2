package com.rabbitmq.learning.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订单实体类
 * 用于演示RabbitMQ在订单业务场景中的应用
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_order")
public class Order extends BaseEntity {

    /**
     * 订单编号 - 业务主键
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 订单状态
     * 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 5-已退款
     */
    private Integer status;

    /**
     * 支付方式
     * 1-支付宝 2-微信 3-银行卡
     */
    private Integer paymentMethod;

    /**
     * 收货地址
     */
    private String shippingAddress;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 订单状态枚举
     */
    public enum OrderStatus {
        PENDING_PAYMENT(0, "待支付"),
        PAID(1, "已支付"),
        SHIPPED(2, "已发货"),
        COMPLETED(3, "已完成"),
        CANCELLED(4, "已取消"),
        REFUNDED(5, "已退款");

        private final Integer code;
        private final String description;

        OrderStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 支付方式枚举
     */
    public enum PaymentMethod {
        ALIPAY(1, "支付宝"),
        WECHAT(2, "微信支付"),
        BANK_CARD(3, "银行卡");

        private final Integer code;
        private final String description;

        PaymentMethod(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
