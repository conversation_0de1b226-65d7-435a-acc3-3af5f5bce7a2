package com.rabbitmq.learning.common.message;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存相关消息
 * 用于库存管理场景中的消息传递
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InventoryMessage extends BaseMessage {

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品SKU编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 操作数量
     */
    private Integer quantity;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 订单编号 - 关联订单
     */
    private String orderNo;

    /**
     * 库存变更前数量
     */
    private Integer beforeQuantity;

    /**
     * 库存变更后数量
     */
    private Integer afterQuantity;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 默认构造函数
     */
    public InventoryMessage() {
        super();
    }

    /**
     * 构造函数
     * 
     * @param productId 商品ID
     * @param quantity 操作数量
     * @param operationType 操作类型
     */
    public InventoryMessage(Long productId, Integer quantity, String operationType) {
        super(productId.toString());
        this.productId = productId;
        this.quantity = quantity;
        this.operationType = operationType;
    }

    /**
     * 构造函数
     * 
     * @param productId 商品ID
     * @param quantity 操作数量
     * @param operationType 操作类型
     * @param orderNo 订单编号
     */
    public InventoryMessage(Long productId, Integer quantity, String operationType, String orderNo) {
        this(productId, quantity, operationType);
        this.orderNo = orderNo;
    }

    /**
     * 库存操作类型枚举
     */
    public static class OperationType {
        public static final String LOCK = "LOCK";               // 锁定库存
        public static final String UNLOCK = "UNLOCK";           // 解锁库存
        public static final String DEDUCT = "DEDUCT";           // 扣减库存
        public static final String INCREASE = "INCREASE";       // 增加库存
        public static final String SYNC = "SYNC";               // 同步库存
        public static final String CHECK = "CHECK";             // 库存检查
        public static final String ALERT = "ALERT";             // 库存预警
    }
}
