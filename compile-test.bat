@echo off
chcp 65001 >nul
echo ========================================
echo 项目编译测试脚本
echo ========================================
echo.

echo 正在检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK 17+
    pause
    exit /b 1
)

echo 正在检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请确保已安装Maven 3.6+
    pause
    exit /b 1
)

echo.
echo ========================================
echo 开始编译项目...
echo ========================================

echo 步骤 1/6: 编译父项目...
call mvn clean compile -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 父项目编译失败
    pause
    exit /b 1
)
echo ✅ 父项目编译成功

echo 步骤 2/6: 编译公共模块...
cd common
call mvn compile -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 公共模块编译失败
    cd ..
    pause
    exit /b 1
)
echo ✅ 公共模块编译成功
cd ..

echo 步骤 3/6: 编译生产者服务...
cd producer-service
call mvn compile -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 生产者服务编译失败
    echo 正在显示详细错误信息...
    call mvn compile -DskipTests
    cd ..
    pause
    exit /b 1
)
echo ✅ 生产者服务编译成功
cd ..

echo 步骤 4/6: 编译消费者服务...
cd consumer-service
call mvn compile -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 消费者服务编译失败
    cd ..
    pause
    exit /b 1
)
echo ✅ 消费者服务编译成功
cd ..

echo 步骤 5/6: 编译订单服务...
cd order-service
call mvn compile -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 订单服务编译失败
    cd ..
    pause
    exit /b 1
)
echo ✅ 订单服务编译成功
cd ..

echo 步骤 6/6: 编译网关服务...
cd gateway-service
call mvn compile -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 网关服务编译失败
    cd ..
    pause
    exit /b 1
)
echo ✅ 网关服务编译成功
cd ..

echo.
echo ========================================
echo 🎉 所有模块编译成功！
echo ========================================
echo.
echo 编译结果:
echo ✅ 父项目
echo ✅ 公共模块 (common)
echo ✅ 生产者服务 (producer-service)
echo ✅ 消费者服务 (consumer-service)
echo ✅ 订单服务 (order-service)
echo ✅ 网关服务 (gateway-service)
echo.
echo 💡 提示: 现在可以启动项目了！
echo 1. 先启动基础服务: docker-compose up -d
echo 2. 再启动微服务: 使用 start.bat 脚本
echo.
pause
