package com.rabbitmq.learning.common.message;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 基础消息类
 * 所有RabbitMQ消息的基类，包含消息的公共属性
 *
 * <AUTHOR> Learning
 * @since 1.0.0
 */
@Data
public class BaseMessage {

    /**
     * 消息唯一标识 - 用于消息去重和追踪
     */
    private String messageId;

    /**
     * 消息类型 - 用于消费者识别消息类型
     */
    private String messageType;

    /**
     * 消息创建时间
     */
    private LocalDateTime createTime;

    /**
     * 消息发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 消息来源服务
     */
    private String sourceService;

    /**
     * 消息目标服务
     */
    private String targetService;

    /**
     * 业务标识 - 用于业务关联
     */
    private String businessId;

    /**
     * 用户ID - 用于用户相关的消息
     */
    private Long userId;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;

    /**
     * 消息优先级 0-9，数字越大优先级越高
     */
    private Integer priority = 5;

    /**
     * 消息过期时间（毫秒）
     */
    private Long expiration;

    /**
     * 扩展属性 - 用于存储额外信息
     */
    private String extendInfo;

    /**
     * 构造函数 - 自动生成消息ID和创建时间
     */
    public BaseMessage() {
        this.messageId = UUID.randomUUID().toString().replace("-", "");
        this.createTime = LocalDateTime.now();
        this.messageType = this.getClass().getSimpleName();
    }

    /**
     * 构造函数 - 指定业务ID
     * 
     * @param businessId 业务标识
     */
    public BaseMessage(String businessId) {
        this();
        this.businessId = businessId;
    }

    /**
     * 构造函数 - 指定业务ID和用户ID
     * 
     * @param businessId 业务标识
     * @param userId 用户ID
     */
    public BaseMessage(String businessId, Long userId) {
        this(businessId);
        this.userId = userId;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 判断是否超过最大重试次数
     * 
     * @return true-超过最大重试次数，false-未超过
     */
    public boolean isExceedMaxRetry() {
        return this.retryCount >= this.maxRetryCount;
    }

    /**
     * 设置消息发送时间为当前时间
     */
    public void setSendTimeNow() {
        this.sendTime = LocalDateTime.now();
    }
}
