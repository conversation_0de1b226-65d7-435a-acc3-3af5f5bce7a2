package com.rabbitmq.learning.common.constant;

/**
 * RabbitMQ相关常量定义
 * 包含交换机、队列、路由键等配置常量
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
public class RabbitMQConstant {

    // ==================== 交换机定义 ====================
    
    /**
     * 订单相关交换机
     */
    public static class Exchange {
        /** 订单直连交换机 */
        public static final String ORDER_DIRECT = "exchange.order.direct";
        
        /** 订单主题交换机 */
        public static final String ORDER_TOPIC = "exchange.order.topic";
        
        /** 订单扇形交换机 */
        public static final String ORDER_FANOUT = "exchange.order.fanout";
        
        /** 库存直连交换机 */
        public static final String INVENTORY_DIRECT = "exchange.inventory.direct";
        
        /** 库存主题交换机 */
        public static final String INVENTORY_TOPIC = "exchange.inventory.topic";
        
        /** 死信交换机 */
        public static final String DEAD_LETTER = "exchange.dead.letter";
        
        /** 延时交换机 */
        public static final String DELAY = "exchange.delay";
    }

    // ==================== 队列定义 ====================
    
    /**
     * 订单相关队列
     */
    public static class Queue {
        /** 订单创建队列 */
        public static final String ORDER_CREATE = "queue.order.create";
        
        /** 订单支付队列 */
        public static final String ORDER_PAYMENT = "queue.order.payment";
        
        /** 订单取消队列 */
        public static final String ORDER_CANCEL = "queue.order.cancel";
        
        /** 订单发货队列 */
        public static final String ORDER_SHIP = "queue.order.ship";
        
        /** 订单完成队列 */
        public static final String ORDER_COMPLETE = "queue.order.complete";
        
        /** 订单超时队列 */
        public static final String ORDER_TIMEOUT = "queue.order.timeout";
        
        /** 库存锁定队列 */
        public static final String INVENTORY_LOCK = "queue.inventory.lock";
        
        /** 库存解锁队列 */
        public static final String INVENTORY_UNLOCK = "queue.inventory.unlock";
        
        /** 库存扣减队列 */
        public static final String INVENTORY_DEDUCT = "queue.inventory.deduct";
        
        /** 库存同步队列 */
        public static final String INVENTORY_SYNC = "queue.inventory.sync";
        
        /** 库存预警队列 */
        public static final String INVENTORY_ALERT = "queue.inventory.alert";
        
        /** 死信队列 */
        public static final String DEAD_LETTER = "queue.dead.letter";
        
        /** 延时队列 */
        public static final String DELAY = "queue.delay";
        
        /** 工作队列 - 演示工作队列模式 */
        public static final String WORK = "queue.work";
        
        /** 发布订阅队列1 - 演示发布订阅模式 */
        public static final String PUBLISH_SUBSCRIBE_1 = "queue.publish.subscribe.1";
        
        /** 发布订阅队列2 - 演示发布订阅模式 */
        public static final String PUBLISH_SUBSCRIBE_2 = "queue.publish.subscribe.2";
        
        /** 路由队列1 - 演示路由模式 */
        public static final String ROUTING_1 = "queue.routing.1";
        
        /** 路由队列2 - 演示路由模式 */
        public static final String ROUTING_2 = "queue.routing.2";
        
        /** 主题队列1 - 演示主题模式 */
        public static final String TOPIC_1 = "queue.topic.1";
        
        /** 主题队列2 - 演示主题模式 */
        public static final String TOPIC_2 = "queue.topic.2";
    }

    // ==================== 路由键定义 ====================
    
    /**
     * 路由键定义
     */
    public static class RoutingKey {
        /** 订单创建路由键 */
        public static final String ORDER_CREATE = "order.create";
        
        /** 订单支付路由键 */
        public static final String ORDER_PAYMENT = "order.payment";
        
        /** 订单取消路由键 */
        public static final String ORDER_CANCEL = "order.cancel";
        
        /** 订单发货路由键 */
        public static final String ORDER_SHIP = "order.ship";
        
        /** 订单完成路由键 */
        public static final String ORDER_COMPLETE = "order.complete";
        
        /** 订单超时路由键 */
        public static final String ORDER_TIMEOUT = "order.timeout";
        
        /** 库存锁定路由键 */
        public static final String INVENTORY_LOCK = "inventory.lock";
        
        /** 库存解锁路由键 */
        public static final String INVENTORY_UNLOCK = "inventory.unlock";
        
        /** 库存扣减路由键 */
        public static final String INVENTORY_DEDUCT = "inventory.deduct";
        
        /** 库存同步路由键 */
        public static final String INVENTORY_SYNC = "inventory.sync";
        
        /** 库存预警路由键 */
        public static final String INVENTORY_ALERT = "inventory.alert";
        
        /** 死信路由键 */
        public static final String DEAD_LETTER = "dead.letter";
        
        /** 延时路由键 */
        public static final String DELAY = "delay";
        
        // 主题模式路由键示例
        /** 用户相关主题 */
        public static final String TOPIC_USER = "user.*";
        
        /** 订单相关主题 */
        public static final String TOPIC_ORDER = "order.*";
        
        /** 库存相关主题 */
        public static final String TOPIC_INVENTORY = "inventory.*";
        
        /** 所有消息主题 */
        public static final String TOPIC_ALL = "#";
    }

    // ==================== 消息属性定义 ====================
    
    /**
     * 消息属性常量
     */
    public static class MessageProperty {
        /** 消息重试次数头 */
        public static final String RETRY_COUNT = "x-retry-count";
        
        /** 消息最大重试次数头 */
        public static final String MAX_RETRY_COUNT = "x-max-retry-count";
        
        /** 消息创建时间头 */
        public static final String CREATE_TIME = "x-create-time";
        
        /** 消息来源服务头 */
        public static final String SOURCE_SERVICE = "x-source-service";
        
        /** 消息目标服务头 */
        public static final String TARGET_SERVICE = "x-target-service";
        
        /** 业务标识头 */
        public static final String BUSINESS_ID = "x-business-id";
        
        /** 用户ID头 */
        public static final String USER_ID = "x-user-id";
    }

    // ==================== 其他常量 ====================
    
    /**
     * 其他常量
     */
    public static class Other {
        /** 默认消息过期时间（毫秒）- 30分钟 */
        public static final long DEFAULT_MESSAGE_TTL = 30 * 60 * 1000L;
        
        /** 订单超时时间（毫秒）- 15分钟 */
        public static final long ORDER_TIMEOUT_TTL = 15 * 60 * 1000L;
        
        /** 默认消息优先级 */
        public static final int DEFAULT_PRIORITY = 5;
        
        /** 最大消息优先级 */
        public static final int MAX_PRIORITY = 10;
        
        /** 默认最大重试次数 */
        public static final int DEFAULT_MAX_RETRY = 3;
    }
}
