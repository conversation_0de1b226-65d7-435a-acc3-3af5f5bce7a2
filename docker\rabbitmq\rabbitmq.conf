# RabbitMQ配置文件

# 网络配置
listeners.tcp.default = 5672
management.tcp.port = 15672

# 用户和权限
default_user = admin
default_pass = admin123
default_vhost = /
default_user_tags.administrator = true
default_permissions.configure = .*
default_permissions.read = .*
default_permissions.write = .*

# 内存和磁盘限制
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0

# 日志配置
log.console = true
log.console.level = info
log.file = /var/log/rabbitmq/rabbit.log
log.file.level = info
log.file.rotation.date = $D0
log.file.rotation.size = 10485760

# 集群配置
cluster_formation.peer_discovery_backend = rabbit_peer_discovery_classic_config
cluster_formation.classic_config.nodes.1 = rabbit@rabbitmq

# 管理插件配置
management.rates_mode = basic
management.sample_retention_policies.global.minute = 5
management.sample_retention_policies.global.hour = 60
management.sample_retention_policies.global.day = 1200

# 队列配置
queue_master_locator = min-masters

# 消息TTL
default_user_tags.management = true

# 心跳配置
heartbeat = 60

# 连接配置
num_acceptors.tcp = 10
handshake_timeout = 10000
reverse_dns_lookups = false

# SSL配置（如果需要）
# ssl_options.cacertfile = /path/to/ca_certificate.pem
# ssl_options.certfile = /path/to/server_certificate.pem
# ssl_options.keyfile = /path/to/server_key.pem
# ssl_options.verify = verify_peer
# ssl_options.fail_if_no_peer_cert = true

# 插件配置
# 启用管理插件
# rabbitmq-plugins enable rabbitmq_management

# 性能调优
channel_max = 2047
frame_max = 131072
collect_statistics = coarse
collect_statistics_interval = 5000

# 消息存储
msg_store_file_size_limit = 16777216
msg_store_credit_disc_bound = 4000

# 队列索引
queue_index_embed_msgs_below = 4096

# 内存配置
vm_memory_calculation_strategy = rss
