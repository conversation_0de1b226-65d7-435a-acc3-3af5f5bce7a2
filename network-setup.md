# Docker 自定义网络配置指南

## 1. 创建自定义网络

### 方法一：使用 docker-compose（推荐）
```bash
# 启动时会自动创建网络
docker-compose up -d

# 查看网络
docker network ls
```

### 方法二：手动创建网络
```bash
# 创建自定义网络
docker network create --driver bridge --subnet=**********/16 app-network

# 查看网络详情
docker network inspect app-network

# 删除网络（如果需要重新创建）
docker network rm app-network
```

## 2. 网络配置说明

### 网络配置详解
```yaml
networks:
  app-network:
    driver: bridge              # 使用桥接网络
    ipam:
      driver: default
      config:
        - subnet: **********/16  # 子网配置
```

### 服务网络连接
每个服务都连接到 `app-network`：
- 容器间可以通过服务名直接通信
- 例如：MySQL容器可以通过 `mysql:3306` 访问
- Redis可以通过 `redis:6379` 访问

## 3. 常用网络命令

```bash
# 查看所有网络
docker network ls

# 查看网络详情
docker network inspect app-network

# 查看容器IP
docker inspect <容器名> | grep IPAddress

# 测试网络连通性
docker exec -it <容器名> ping mysql
docker exec -it <容器名> ping redis
```

## 4. 故障排除

### 网络问题排查
```bash
# 检查网络是否存在
docker network ls | grep app-network

# 如果网络不存在，手动创建
docker network create app-network

# 重新启动服务
docker-compose down
docker-compose up -d
```

### 容器网络测试
```bash
# 进入容器测试网络连接
docker exec -it <服务名> sh

# 在容器内测试
nslookup mysql
nslookup redis
ping mysql
ping redis
```

## 5. 高级配置

### 固定容器IP（可选）
```yaml
services:
  mysql:
    networks:
      app-network:
        ipv4_address: ***********
  
  redis:
    networks:
      app-network:
        ipv4_address: ***********
```

### 外部网络访问
```bash
# 从主机访问容器
curl http://localhost:9190
curl http://localhost:33067
curl http://localhost:6481
```