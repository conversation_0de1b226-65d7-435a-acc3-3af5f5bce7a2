package com.rabbitmq.learning.common.result;

/**
 * 响应结果码枚举
 * 定义系统中所有可能的响应码和对应的消息
 * 
 * <AUTHOR> Learning
 * @since 1.0.0
 */
public enum ResultCode {

    // 通用响应码 (1000-1999)
    SUCCESS(200, "操作成功"),
    ERROR(500, "系统错误"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 业务相关响应码 (2000-2999)
    BUSINESS_ERROR(2000, "业务处理失败"),
    DATA_NOT_FOUND(2001, "数据不存在"),
    DATA_ALREADY_EXISTS(2002, "数据已存在"),
    DATA_INVALID(2003, "数据无效"),
    OPERATION_NOT_ALLOWED(2004, "操作不被允许"),

    // 订单相关响应码 (3000-3099)
    ORDER_NOT_FOUND(3000, "订单不存在"),
    ORDER_STATUS_ERROR(3001, "订单状态错误"),
    ORDER_CANNOT_CANCEL(3002, "订单无法取消"),
    ORDER_PAYMENT_FAILED(3003, "订单支付失败"),
    ORDER_ALREADY_PAID(3004, "订单已支付"),
    ORDER_TIMEOUT(3005, "订单超时"),

    // 库存相关响应码 (3100-3199)
    INVENTORY_NOT_FOUND(3100, "库存不存在"),
    INVENTORY_INSUFFICIENT(3101, "库存不足"),
    INVENTORY_LOCK_FAILED(3102, "库存锁定失败"),
    INVENTORY_UNLOCK_FAILED(3103, "库存解锁失败"),
    INVENTORY_DEDUCT_FAILED(3104, "库存扣减失败"),

    // 消息相关响应码 (4000-4099)
    MESSAGE_SEND_FAILED(4000, "消息发送失败"),
    MESSAGE_CONSUME_FAILED(4001, "消息消费失败"),
    MESSAGE_RETRY_EXCEEDED(4002, "消息重试次数超限"),
    MESSAGE_FORMAT_ERROR(4003, "消息格式错误"),
    MESSAGE_DUPLICATE(4004, "重复消息"),

    // 系统相关响应码 (5000-5099)
    SYSTEM_BUSY(5000, "系统繁忙"),
    SYSTEM_MAINTENANCE(5001, "系统维护中"),
    DATABASE_ERROR(5002, "数据库错误"),
    REDIS_ERROR(5003, "Redis错误"),
    RABBITMQ_ERROR(5004, "RabbitMQ错误"),
    NETWORK_ERROR(5005, "网络错误"),
    TIMEOUT_ERROR(5006, "请求超时");

    /**
     * 响应码
     */
    private final Integer code;

    /**
     * 响应消息
     */
    private final String message;

    /**
     * 构造函数
     * 
     * @param code 响应码
     * @param message 响应消息
     */
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取响应码
     * 
     * @return 响应码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取响应消息
     * 
     * @return 响应消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 根据响应码获取枚举
     * 
     * @param code 响应码
     * @return 结果码枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return ERROR;
    }
}
